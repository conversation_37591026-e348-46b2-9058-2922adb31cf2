import json
import os
import time
import subprocess
import threading
import asyncio
import concurrent.futures
import re
import ast
import shutil
import glob
import urllib.request
import urllib.parse
import queue
import difflib
import tempfile
import zipfile
import tarfile
import sqlite3
import requests
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import pickle
import logging
from collections import defaultdict, deque
import weakref
import gc
import random
import uuid
import mimetypes
import base64
import secrets
from functools import lru_cache, wraps
import aiohttp
import aiofiles
from contextlib import asynccontextmanager
import psutil
import platform

# Modern TUI imports for OpenCode-like interface
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.layout import Layout
    from rich.live import Live
    from rich.text import Text
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.syntax import Syntax
    from rich.markdown import Markdown
    from rich.prompt import Prompt, Confirm
    from rich.columns import Columns
    from rich.align import Align
    from rich.padding import Padding
    from rich.rule import Rule
    from rich.status import Status
    from rich.tree import Tree
    from rich.box import ROUNDED, DOUBLE, HEAVY
    from rich.spinner import Spinner
    from rich.traceback import install
    from rich.json import JSON
    from rich.filesize import decimal
    from rich.highlighter import ReprHighlighter
    from rich.theme import Theme
    RICH_AVAILABLE = True
    install(show_locals=True)  # Better error tracebacks
except ImportError:
    RICH_AVAILABLE = False
    print("⚠️ Rich not available. Install with: pip install rich aiohttp aiofiles")

# Advanced parsing and analysis
try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.tools import Tool
from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferWindowMemory
from langchain.callbacks import StreamingStdOutCallbackHandler

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Advanced API Key Load Balancer
class APIKeyLoadBalancer:
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.key_usage = {key: {'requests': 0, 'tokens': 0, 'last_used': datetime.now()} for key in self.api_keys}
        self.current_key_index = 0
        self.rate_limits = {key: {'requests_per_minute': 60, 'tokens_per_minute': 100000} for key in self.api_keys}
        self.lock = threading.Lock()

    def _load_api_keys(self) -> List[str]:
        """Load multiple API keys from environment"""
        keys = []
        # Primary key
        primary_key = os.getenv("GEMINI_API_KEY")
        if primary_key:
            keys.append(primary_key)

        # Additional keys (GEMINI_API_KEY_2, GEMINI_API_KEY_3, etc.)
        i = 2
        while True:
            key = os.getenv(f"GEMINI_API_KEY_{i}")
            if key:
                keys.append(key)
                i += 1
            else:
                break

        return keys if keys else ["dummy_key_for_testing"]

    def get_best_key(self, estimated_tokens: int = 1000) -> str:
        """Get the best available API key based on usage and rate limits"""
        with self.lock:
            if not self.api_keys:
                return "dummy_key"

            now = datetime.now()
            best_key = None
            min_usage = float('inf')

            for key in self.api_keys:
                usage = self.key_usage[key]

                # Reset counters if more than a minute has passed
                if (now - usage['last_used']).seconds > 60:
                    usage['requests'] = 0
                    usage['tokens'] = 0

                # Check if key is within rate limits
                if (usage['requests'] < self.rate_limits[key]['requests_per_minute'] and
                    usage['tokens'] + estimated_tokens < self.rate_limits[key]['tokens_per_minute']):

                    current_usage = usage['requests'] + (usage['tokens'] / 1000)
                    if current_usage < min_usage:
                        min_usage = current_usage
                        best_key = key

            if best_key:
                self.key_usage[best_key]['requests'] += 1
                self.key_usage[best_key]['tokens'] += estimated_tokens
                self.key_usage[best_key]['last_used'] = now
                return best_key

            # If all keys are rate limited, return the least used one
            return min(self.api_keys, key=lambda k: self.key_usage[k]['requests'])

# Initialize Load Balancer
api_balancer = APIKeyLoadBalancer()

# Smart LLM Manager with Load Balancing
class SmartLLMManager:
    def __init__(self):
        self.models = {}
        self.current_model = None
        self.token_optimizer = TokenOptimizer()
        self.initialize_models()

    def initialize_models(self):
        """Initialize multiple LLM instances with different API keys"""
        try:
            for i, key in enumerate(api_balancer.api_keys):
                if key != "dummy_key_for_testing":
                    self.models[f"gemini_{i}"] = ChatGoogleGenerativeAI(
                        model="gemini-2.0-flash",
                        google_api_key=key,
                        temperature=0.1,
                        streaming=True,
                        callbacks=[StreamingStdOutCallbackHandler()]
                    )

            if self.models:
                self.current_model = list(self.models.values())[0]
                print(f"✅ LLM initialized with {len(self.models)} API keys")
            else:
                raise Exception("No valid API keys found")

        except Exception as e:
            print(f"⚠️ LLM initialization failed: {e}")
            # Create a dummy LLM for testing
            class DummyLLM:
                def invoke(self, messages):
                    class DummyResponse:
                        content = "This is a dummy response for testing purposes."
                    return DummyResponse()
            self.current_model = DummyLLM()
            print("✅ Using dummy LLM for testing")

    def get_optimized_response(self, messages, estimated_tokens: int = 1000):
        """Get response with optimized token usage and load balancing"""
        # Optimize the prompt to reduce tokens
        optimized_messages = self.token_optimizer.optimize_messages(messages)

        # Get best API key
        best_key = api_balancer.get_best_key(estimated_tokens)

        # Select appropriate model
        model_key = f"gemini_{api_balancer.api_keys.index(best_key)}" if best_key in api_balancer.api_keys else "gemini_0"
        selected_model = self.models.get(model_key, self.current_model)

        return selected_model.invoke(optimized_messages)

# Token Optimization System
class TokenOptimizer:
    def __init__(self):
        self.compression_patterns = {
            # Common code patterns that can be compressed
            r'\s+': ' ',  # Multiple spaces to single space
            r'\n\s*\n': '\n',  # Multiple newlines to single
            r'#.*?\n': '\n',  # Remove comments (be careful with this)
            r'""".*?"""': '"""[docstring]"""',  # Compress docstrings
        }

    def optimize_messages(self, messages) -> List:
        """Optimize messages to reduce token count"""
        optimized = []

        for message in messages:
            if hasattr(message, 'content'):
                content = message.content

                # Apply compression patterns
                for pattern, replacement in self.compression_patterns.items():
                    content = re.sub(pattern, replacement, content, flags=re.DOTALL)

                # Truncate very long content
                if len(content) > 8000:  # Approximate token limit
                    content = content[:7500] + "\n... [content truncated for optimization]"

                # Create new message with optimized content
                optimized_message = type(message)(content=content)
                optimized.append(optimized_message)
            else:
                optimized.append(message)

        return optimized

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        # Rough estimation: 1 token ≈ 4 characters
        return len(text) // 4

# Initialize Smart LLM Manager
llm_manager = SmartLLMManager()
llm = llm_manager.current_model

# Modern UI System with Streaming
class ModernUI:
    def __init__(self):
        self.console = Console(theme=Theme({
            "info": "cyan",
            "warning": "yellow",
            "error": "red bold",
            "success": "green bold",
            "code": "blue",
            "highlight": "magenta bold"
        }))
        self.layout = Layout()
        self.setup_layout()
        self.live = None

    def setup_layout(self):
        """Setup the modern terminal layout"""
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        self.layout["main"].split_row(
            Layout(name="sidebar", size=30),
            Layout(name="content")
        )

        self.layout["content"].split_column(
            Layout(name="output", ratio=3),
            Layout(name="input", size=5)
        )

    def create_header(self, status: str = "Ready") -> Panel:
        """Create dynamic header panel"""
        header_text = Text()
        header_text.append("🤖 ", style="bold blue")
        header_text.append("Advanced AI Coding Agent", style="bold white")
        header_text.append(f" | Status: {status}", style="info")
        header_text.append(f" | {datetime.now().strftime('%H:%M:%S')}", style="dim")

        return Panel(
            Align.center(header_text),
            style="blue",
            box=HEAVY
        )

    def create_sidebar(self, context: 'AgentContext') -> Panel:
        """Create dynamic sidebar with context info"""
        tree = Tree("📊 Context", style="bold blue")

        # Project info
        project_node = tree.add("📁 Project")
        project_node.add(f"Directory: {Path(context.current_directory).name}")
        project_node.add(f"Files: {len(context.active_files)}")

        # Recent commands
        cmd_node = tree.add("⚡ Commands")
        for cmd in context.command_history[-3:]:
            cmd_node.add(f"• {cmd[:20]}...")

        # Predictions
        pred_node = tree.add("🔮 Predictions")
        for pred in context.predictive_cache.next_actions[:3]:
            pred_node.add(f"• {pred}")

        return Panel(tree, title="Context", border_style="blue")

    def create_footer(self, stats: Dict) -> Panel:
        """Create dynamic footer with stats"""
        footer_text = Text()
        footer_text.append(f"Memory: {stats.get('memory', 0)}% ", style="info")
        footer_text.append(f"CPU: {stats.get('cpu', 0)}% ", style="info")
        footer_text.append(f"API Calls: {stats.get('api_calls', 0)} ", style="info")
        footer_text.append(f"Tokens: {stats.get('tokens', 0)}", style="info")

        return Panel(
            Align.center(footer_text),
            style="dim",
            box=ROUNDED
        )

    def stream_response(self, response_generator, title: str = "AI Response"):
        """Stream AI response with real-time updates"""
        with self.console.status(f"[bold blue]Processing {title}...") as status:
            response_text = ""
            for chunk in response_generator:
                response_text += chunk
                # Update display in real-time
                self.console.print(f"\r{chunk}", end="", style="info")
                time.sleep(0.01)  # Small delay for smooth streaming

        return response_text

    def display_code(self, code: str, language: str = "python", title: str = "Code"):
        """Display code with syntax highlighting"""
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        self.console.print(Panel(syntax, title=title, border_style="blue"))

    def display_error(self, error: str, context: str = ""):
        """Display error with context"""
        error_panel = Panel(
            f"[red bold]Error:[/red bold] {error}\n[dim]{context}[/dim]",
            title="❌ Error",
            border_style="red"
        )
        self.console.print(error_panel)

    def display_success(self, message: str, details: str = ""):
        """Display success message"""
        success_panel = Panel(
            f"[green bold]{message}[/green bold]\n[dim]{details}[/dim]",
            title="✅ Success",
            border_style="green"
        )
        self.console.print(success_panel)

# Advanced Context and State Management
@dataclass
class PredictiveCache:
    suggestions: Dict[str, List[str]] = field(default_factory=dict)
    code_snippets: Dict[str, str] = field(default_factory=dict)
    next_actions: List[str] = field(default_factory=list)
    context_patterns: Dict[str, int] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    api_usage: Dict[str, int] = field(default_factory=dict)

@dataclass
class CodeAnalysisResult:
    complexity: int = 0
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    duplicates: List[Dict] = field(default_factory=list)
    security_issues: List[str] = field(default_factory=list)
    performance_issues: List[str] = field(default_factory=list)
    refactor_suggestions: List[str] = field(default_factory=list)

@dataclass
class AgentContext:
    current_directory: str = os.getcwd()
    active_files: List[str] = field(default_factory=list)
    command_history: List[str] = field(default_factory=list)
    project_structure: Dict = field(default_factory=dict)
    last_error: str = ""
    working_memory: Dict = field(default_factory=dict)
    predictive_cache: PredictiveCache = field(default_factory=PredictiveCache)
    code_analysis: Dict[str, CodeAnalysisResult] = field(default_factory=dict)
    language_preferences: Dict[str, str] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    git_status: Dict = field(default_factory=dict)
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_preferences: Dict = field(default_factory=dict)
    project_metadata: Dict = field(default_factory=dict)
    security_context: Dict = field(default_factory=dict)
    optimization_settings: Dict = field(default_factory=dict)

    def __post_init__(self):
        if not self.active_files:
            self.active_files = []
        if not self.command_history:
            self.command_history = []
        if not self.working_memory:
            self.working_memory = {}
        if not self.user_preferences:
            self.user_preferences = {
                'ui_theme': 'dark',
                'code_style': 'modern',
                'auto_save': True,
                'streaming_response': True,
                'show_predictions': True
            }
        if not self.optimization_settings:
            self.optimization_settings = {
                'token_optimization': True,
                'response_caching': True,
                'predictive_prefetch': True,
                'smart_compression': True
            }

class PredictivePrefetcher:
    def __init__(self, agent_context):
        self.context = agent_context
        self.prediction_queue = queue.Queue()
        self.suggestion_cache = {}
        self.pattern_analyzer = PatternAnalyzer()
        self.is_running = False

    def start_background_prediction(self):
        """Start background prediction thread"""
        if not self.is_running:
            self.is_running = True
            threading.Thread(target=self._prediction_worker, daemon=True).start()

    def _prediction_worker(self):
        """Background worker for predictive prefetching"""
        while self.is_running:
            try:
                # Analyze current context and predict next actions
                predictions = self._generate_predictions()
                self.context.predictive_cache.next_actions = predictions
                time.sleep(2)  # Update every 2 seconds
            except Exception as e:
                logging.error(f"Prediction worker error: {e}")

    def _generate_predictions(self):
        """Generate predictions based on current context"""
        predictions = []

        # Analyze command history patterns
        if len(self.context.command_history) >= 2:
            last_commands = self.context.command_history[-3:]
            patterns = self.pattern_analyzer.analyze_command_patterns(last_commands)
            predictions.extend(patterns)

        # Analyze file context
        if self.context.active_files:
            file_predictions = self.pattern_analyzer.analyze_file_patterns(self.context.active_files)
            predictions.extend(file_predictions)

        return predictions[:10]  # Top 10 predictions

class PatternAnalyzer:
    def __init__(self):
        self.command_patterns = {
            ('git', 'add'): ['git commit -m "Update"', 'git push'],
            ('npm', 'install'): ['npm start', 'npm run dev', 'npm test'],
            ('pip', 'install'): ['python -m pytest', 'python main.py'],
            ('create', 'file'): ['edit file', 'run file', 'test file'],
            ('write', 'code'): ['run code', 'test code', 'debug code']
        }

    def analyze_command_patterns(self, commands):
        """Analyze command patterns and suggest next actions"""
        suggestions = []
        for i in range(len(commands) - 1):
            pattern = tuple(commands[i].split()[:2])
            if pattern in self.command_patterns:
                suggestions.extend(self.command_patterns[pattern])
        return suggestions

    def analyze_file_patterns(self, files):
        """Analyze file patterns and suggest actions"""
        suggestions = []
        for file_path in files:
            ext = Path(file_path).suffix.lower()
            if ext == '.py':
                suggestions.extend(['run python file', 'test python code', 'lint python code'])
            elif ext in ['.js', '.ts']:
                suggestions.extend(['run node file', 'test javascript', 'build project'])
            elif ext == '.html':
                suggestions.extend(['open in browser', 'validate html', 'test responsive'])
        return suggestions

class AdvancedCodingAgent:
    def __init__(self):
        self.context = AgentContext()
        self.ui = ModernUI()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=16)
        self.memory = ConversationBufferWindowMemory(k=20, return_messages=True)
        self.cache = {}
        self.running_processes = {}
        self.predictive_prefetcher = PredictivePrefetcher(self.context)
        self.code_analyzer = AdvancedCodeAnalyzer()
        self.language_converter = LanguageConverter()
        self.refactoring_engine = RefactoringEngine()
        self.web_scraper = EnhancedWebScraper()
        self.git_manager = GitManager()
        self.package_manager = PackageManager()
        self.llm_manager = llm_manager
        self.token_optimizer = TokenOptimizer()

        # Advanced capabilities
        self.file_manager = SmartFileManager()
        self.project_analyzer = ProjectAnalyzer()
        self.security_scanner = SecurityScanner()
        self.performance_profiler = PerformanceProfiler()
        self.code_generator = SmartCodeGenerator()
        self.test_runner = IntelligentTestRunner()
        self.documentation_generator = DocumentationGenerator()

        # Real-time monitoring
        self.performance_monitor = PerformanceMonitor()
        self.error_tracker = ErrorTracker()
        self.usage_analytics = UsageAnalytics()

        # Start background services
        self.predictive_prefetcher.start_background_prediction()
        self.performance_monitor.start_monitoring()
        self._setup_logging()
        self._initialize_capabilities()

    def _setup_logging(self):
        """Setup advanced logging for the agent"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('agent.log'),
                logging.StreamHandler()
            ]
        )

    def _initialize_capabilities(self):
        """Initialize all advanced capabilities"""
        self.ui.console.print("🚀 Initializing Advanced Capabilities...", style="info")

        capabilities = [
            ("File Management", self.file_manager),
            ("Project Analysis", self.project_analyzer),
            ("Security Scanning", self.security_scanner),
            ("Performance Profiling", self.performance_profiler),
            ("Code Generation", self.code_generator),
            ("Test Running", self.test_runner),
            ("Documentation", self.documentation_generator)
        ]

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=self.ui.console
        ) as progress:
            task = progress.add_task("Loading capabilities...", total=len(capabilities))

            for name, capability in capabilities:
                progress.update(task, description=f"Loading {name}...")
                time.sleep(0.1)  # Simulate loading time
                progress.advance(task)

        self.ui.console.print("✅ All capabilities loaded successfully!", style="success")

    def run_command(self, command: str, timeout: int = 30) -> str:
        """Execute PowerShell commands with advanced error handling"""
        try:
            self.context.command_history.append(command)

            # Use PowerShell for Windows
            if os.name == 'nt':
                cmd = ['powershell', '-Command', command]
            else:
                cmd = command

            result = subprocess.run(
                cmd,
                shell=True if os.name != 'nt' else False,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.context.current_directory
            )

            output = result.stdout.strip() if result.stdout else ""
            error = result.stderr.strip() if result.stderr else ""

            if result.returncode == 0:
                return f"✅ Command executed successfully:\n{output}"
            else:
                self.context.last_error = error
                return f"❌ Command failed (code {result.returncode}):\n{error}\nOutput: {output}"

        except subprocess.TimeoutExpired:
            return f"⏰ Command timed out after {timeout} seconds"
        except Exception as e:
            error_msg = f"❌ Error executing command: {str(e)}"
            self.context.last_error = error_msg
            return error_msg

    def write_file(self, path: str, content: str, backup: bool = True) -> str:
        """Advanced file writing with backup and validation"""
        try:
            abs_path = os.path.abspath(path)
            dir_path = os.path.dirname(abs_path)

            # Create backup if file exists
            if backup and os.path.exists(abs_path):
                backup_path = f"{abs_path}.backup_{int(time.time())}"
                shutil.copy2(abs_path, backup_path)

            # Ensure directory exists
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write file with encoding
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            # Validate written content
            with open(abs_path, 'r', encoding='utf-8') as f:
                written_content = f.read()

            if written_content == content:
                return f"✅ File '{path}' written successfully ({len(content)} chars)"
            else:
                return f"⚠️ File written but content validation failed"

        except Exception as e:
            return f"❌ Error writing file '{path}': {str(e)}"

    def read_file(self, file_path: str, lines: Optional[Tuple[int, int]] = None) -> str:
        """Advanced file reading with line range support"""
        try:
            abs_path = os.path.abspath(file_path)

            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"

            with open(abs_path, 'r', encoding='utf-8') as f:
                if lines:
                    all_lines = f.readlines()
                    start, end = lines
                    selected_lines = all_lines[start-1:end] if end != -1 else all_lines[start-1:]
                    content = ''.join(selected_lines)
                else:
                    content = f.read()

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            return f"✅ File content ({len(content)} chars):\n{content}"

        except Exception as e:
            return f"❌ Error reading file '{file_path}': {str(e)}"

    def search_files(self, pattern: str, directory: str = ".", file_types: List[str] = None) -> str:
        """Search for files and content with advanced filtering"""
        try:
            if file_types is None:
                file_types = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json", "*.md", "*.txt", "*.yml", "*.yaml", "*.sh", "*.bash", "*.ps1", "*.cmd", "*.bat", "*.ini", "*.cfg", "*.conf", "*.xml", "*.csv", "*.log"]

            results = []
            search_dir = os.path.abspath(directory)

            for file_type in file_types:
                for file_path in glob.glob(os.path.join(search_dir, "**", file_type), recursive=True):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if re.search(pattern, content, re.IGNORECASE):
                                # Find matching lines
                                lines = content.split('\n')
                                matches = []
                                for i, line in enumerate(lines, 1):
                                    if re.search(pattern, line, re.IGNORECASE):
                                        matches.append(f"  Line {i}: {line.strip()}")

                                results.append(f"📁 {file_path}:\n" + "\n".join(matches[:5]))
                    except:
                        continue

            if results:
                return f"🔍 Found {len(results)} files matching '{pattern}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No files found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error searching files: {str(e)}"

    def get_web_info(self, query: str) -> str:
        """Retrieve information from web without search engine API"""
        try:
            # Simple web scraping for documentation and info
            encoded_query = urllib.parse.quote(query)
            urls = [
                f"https://docs.python.org/3/search.html?q={encoded_query}",
                f"https://developer.mozilla.org/en-US/search?q={encoded_query}",
                f"https://stackoverflow.com/search?q={encoded_query}"
            ]

            results = []
            for url in urls[:2]:  # Limit to avoid rate limiting
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        content = response.read().decode('utf-8')
                        # Extract useful text (simplified)
                        text_content = re.sub(r'<[^>]+>', '', content)
                        text_content = re.sub(r'\s+', ' ', text_content)
                        results.append(text_content[:500] + "...")
                except:
                    continue

            if results:
                return f"🌐 Web information for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ Could not retrieve web information for '{query}'"

        except Exception as e:
            return f"❌ Error retrieving web info: {str(e)}"

    def analyze_code(self, code: str, language: str = "python") -> str:
        """Advanced code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = {
                        "functions": [],
                        "classes": [],
                        "imports": [],
                        "variables": [],
                        "complexity": 0
                    }

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis["functions"].append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis["classes"].append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis["imports"].append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis["imports"].append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis["variables"].append(target.id)

                    return f"📊 Code Analysis:\n{json.dumps(analysis, indent=2)}"
                except SyntaxError as e:
                    return f"❌ Syntax Error in code: {str(e)}"
            else:
                return f"🔍 Basic analysis for {language} code:\nLines: {len(code.split())}\nCharacters: {len(code)}"

        except Exception as e:
            return f"❌ Error analyzing code: {str(e)}"

    def fix_errors(self, error_log: str, code_context: str = "") -> str:
        """Advanced error analysis and fixing suggestions"""
        try:
            suggestions = []
            fixes = []

            # Common error patterns and fixes
            error_patterns = {
                r"ModuleNotFoundError.*'(\w+)'": lambda m: f"pip install {m.group(1)}",
                r"SyntaxError.*line (\d+)": lambda m: f"Check syntax on line {m.group(1)}",
                r"IndentationError": lambda m: "Fix indentation - use consistent spaces/tabs",
                r"NameError.*'(\w+)'": lambda m: f"Variable '{m.group(1)}' not defined - check spelling",
                r"FileNotFoundError.*'([^']+)'": lambda m: f"File '{m.group(1)}' not found - check path",
                r"port.*already in use": lambda m: "Change port number or kill existing process",
                r"Permission denied": lambda m: "Run with administrator privileges or check file permissions"
            }

            for pattern, fix_func in error_patterns.items():
                matches = re.finditer(pattern, error_log, re.IGNORECASE)
                for match in matches:
                    fix = fix_func(match)
                    if fix not in fixes:
                        fixes.append(fix)

            # AI-powered suggestions based on context
            if code_context:
                if "import" in error_log.lower() and "module" in error_log.lower():
                    missing_modules = re.findall(r"No module named '(\w+)'", error_log)
                    for module in missing_modules:
                        fixes.append(f"Install missing module: pip install {module}")

            if fixes:
                return f"🔧 Error Analysis & Fixes:\n" + "\n".join([f"• {fix}" for fix in fixes])
            else:
                return f"🤔 Complex error detected. Manual review needed:\n{error_log[:500]}"

        except Exception as e:
            return f"❌ Error analyzing error log: {str(e)}"

    def generate_code(self, description: str, language: str = "python") -> str:
        """AI-powered code generation"""
        try:
            prompt = f"""Generate {language} code for: {description}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comments for complex logic
- Follow best practices for {language}
- Make it modular and reusable

Code:"""

            # Use LangChain to generate code
            response = llm.invoke([HumanMessage(content=prompt)])
            generated_code = response.content

            # Extract code from response
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', generated_code, re.DOTALL)
            if code_match:
                return f"🤖 Generated {language} code:\n```{language}\n{code_match.group(1)}\n```"
            else:
                return f"🤖 Generated {language} code:\n{generated_code}"

        except Exception as e:
            return f"❌ Error generating code: {str(e)}"

    def refactor_code(self, code: str, refactor_type: str = "optimize") -> str:
        """AI-powered code refactoring"""
        try:
            refactor_prompts = {
                "optimize": "Optimize this code for better performance and readability",
                "modularize": "Break this code into smaller, reusable functions/modules",
                "clean": "Clean up this code - remove duplicates, improve naming, add comments",
                "secure": "Make this code more secure - fix potential vulnerabilities"
            }

            prompt = f"""{refactor_prompts.get(refactor_type, refactor_prompts['optimize'])}:

Original Code:
```
{code}
```

Refactored Code:"""

            response = llm.invoke([HumanMessage(content=prompt)])
            return f"🔄 Refactored code ({refactor_type}):\n{response.content}"

        except Exception as e:
            return f"❌ Error refactoring code: {str(e)}"

    def get_project_structure(self, directory: str = ".") -> str:
        """Get comprehensive project structure"""
        try:
            structure = {}

            def build_tree(path, max_depth=3, current_depth=0):
                if current_depth >= max_depth:
                    return "..."

                items = {}
                try:
                    for item in sorted(os.listdir(path)):
                        if item.startswith('.'):
                            continue
                        item_path = os.path.join(path, item)
                        if os.path.isdir(item_path):
                            items[f"📁 {item}/"] = build_tree(item_path, max_depth, current_depth + 1)
                        else:
                            size = os.path.getsize(item_path)
                            items[f"📄 {item}"] = f"{size} bytes"
                except PermissionError:
                    items["❌ Permission Denied"] = ""
                return items

            structure = build_tree(os.path.abspath(directory))
            self.context.project_structure = structure

            return f"📂 Project Structure:\n{json.dumps(structure, indent=2)}"

        except Exception as e:
            return f"❌ Error getting project structure: {str(e)}"

    def run_tests(self, test_path: str = ".", test_type: str = "auto") -> str:
        """Run tests with auto-detection"""
        try:
            test_commands = {
                "python": ["python -m pytest", "python -m unittest discover"],
                "javascript": ["npm test", "yarn test", "jest"],
                "node": ["npm test", "mocha"],
                "auto": []
            }

            if test_type == "auto":
                # Auto-detect test framework
                if os.path.exists("package.json"):
                    test_commands["auto"] = test_commands["javascript"]
                elif any(f.endswith(".py") for f in os.listdir(".")):
                    test_commands["auto"] = test_commands["python"]
                else:
                    return "❌ Could not auto-detect test framework"

            commands = test_commands.get(test_type, test_commands["auto"])

            for cmd in commands:
                result = self.run_command(cmd)
                if "✅" in result:
                    return f"🧪 Tests executed:\n{result}"

            return "❌ No suitable test command found"

        except Exception as e:
            return f"❌ Error running tests: {str(e)}"

    def show_help(self):
        """Show comprehensive help information"""
        help_text = """
🤖 ADVANCED CLI CODING AGENT v2.0 - COMPREHENSIVE HELP

🎯 ENTERPRISE-LEVEL CAPABILITIES:
• Build complete applications in 10+ programming languages
• Cross-language code conversion (Python ↔ JavaScript ↔ TypeScript ↔ C++ ↔ Java ↔ Go)
• Deep code analysis with security and performance auditing
• Multi-step automated pipelines (Generate → Run → Fix → Refactor → Optimize)
• Enhanced web research with Stack Overflow, GitHub, and documentation integration
• Advanced Git operations and automated version control
• Intelligent package management across all major ecosystems
• Predictive suggestions with background processing
• Real-time performance profiling and optimization

💡 EXAMPLE COMMANDS:

🏗️ PROJECT CREATION & MANAGEMENT:
• "Create a full-stack React TypeScript app with authentication"
• "Build a Python FastAPI microservice with Docker"
• "Set up a Rust CLI application with error handling"
• "Initialize a Node.js project with Express and MongoDB"

🔄 CODE TRANSFORMATION & ANALYSIS:
• "Convert this Python function to JavaScript"
• "Analyze the security vulnerabilities in my code"
• "Profile the performance of this algorithm"
• "Refactor this code for better maintainability"
• "Generate comprehensive unit tests for my module"

🔍 INTELLIGENT RESEARCH & DEBUGGING:
• "Search Stack Overflow for React hooks best practices"
• "Find GitHub examples of JWT authentication"
• "Debug this error and provide automated fixes"
• "Research the latest TypeScript features"

📦 DEPENDENCY & VERSION CONTROL:
• "Install and configure all project dependencies"
• "Commit my changes with an intelligent message"
• "Update all packages to latest versions"
• "Set up automated testing pipeline"

🧠 SMART AUTOMATION:
• "Run the complete development workflow"
• "Optimize my code for production deployment"
• "Set up CI/CD pipeline with GitHub Actions"
• "Generate API documentation automatically"

🔧 SPECIAL COMMANDS:
• help - Show this comprehensive help
• status - Show detailed agent status and context
• suggestions - Get smart suggestions based on current context
• pipeline [description] - Run multi-step automation pipeline
• convert [code] [from_lang] [to_lang] - Convert code between languages
• audit [code] - Perform security and performance audit
• profile [code] - Profile code performance
• git [operation] - Perform Git operations
• install [package] - Install packages with auto-detection
• exit/quit - Exit the agent

🚀 AUTONOMOUS ENTERPRISE FEATURES:
• Predictive prefetching of next likely actions
• Context-aware intelligent suggestions
• Auto-detection of project type and requirements
• Cross-language code translation and optimization
• Multi-threaded execution with zero-lag responses
• Background error monitoring and auto-fixing
• Intelligent Git workflow automation
• Performance optimization recommendations
• Security vulnerability detection and remediation
• Automated code review and quality enforcement
• Documentation generation and maintenance
• Real-time project health monitoring

🧠 INTELLIGENCE CAPABILITIES:
• Natural language understanding (English/Hindi)
• Pattern recognition for task automation
• Contextual learning from user preferences
• Predictive code completion and suggestions
• Chain-of-thought reasoning for complex problems
• Self-critique and continuous improvement
• Multi-source web research and synthesis
• Automated testing and validation

🔒 SECURITY & PERFORMANCE:
• Comprehensive security auditing
• Performance profiling and optimization
• Code quality enforcement
• Best practices validation
• Vulnerability detection and remediation
• Automated security patches

📊 ANALYTICS & MONITORING:
• Real-time performance metrics
• Code complexity analysis
• Project health monitoring
• Development productivity tracking
• Error pattern analysis
• Optimization recommendations
"""
        print(help_text)

    def show_status(self):
        """Show comprehensive agent status with modern UI"""
        try:
            # Get system stats
            stats = self.performance_monitor.get_current_stats()

            # Create status display
            status_panel = Panel(
                f"""[bold blue]🤖 Advanced AI Coding Agent v3.0[/bold blue]

[info]📊 Performance:[/info]
• CPU: {stats['cpu_avg']:.1f}%
• Memory: {stats['memory_avg']:.1f}%
• API Calls: {stats['api_calls']}
• Avg Response: {stats['avg_response_time']:.2f}s

[info]📁 Context:[/info]
• Directory: {Path(self.context.current_directory).name}
• Active Files: {len(self.context.active_files)}
• Commands: {len(self.context.command_history)}

[info]🔮 Intelligence:[/info]
• Predictions: {len(self.context.predictive_cache.next_actions)}
• Cache Size: {len(self.cache)}
• Errors: {stats['error_count']}

[success]✅ All systems operational[/success]""",
                title="Agent Status",
                border_style="blue"
            )

            self.ui.console.print(status_panel)

        except Exception as e:
            self.ui.display_error(f"Status display error: {str(e)}")

    def run_agent(self):
        """Main agent execution loop with modern UI and streaming"""
        # Display modern welcome screen
        welcome_panel = Panel(
            f"""[bold blue]🤖 Advanced AI Coding Agent v3.0 - Enterprise Edition[/bold blue]

[info]🎯 Capabilities:[/info]
• Build complete applications in 25+ languages
• Real-time streaming responses with load balancing
• Advanced security scanning and performance profiling
• Cross-language code conversion and optimization
• Intelligent project analysis and recommendations
• Smart file management with predictive caching

[info]💡 Quick Examples:[/info]
• "Create a full-stack React TypeScript app"
• "Convert this Python code to Rust"
• "Audit my code for security vulnerabilities"
• "Profile performance and optimize"
• "Generate comprehensive documentation"

[info]🔧 Commands:[/info]
• [bold]help[/bold] - Full capabilities guide
• [bold]status[/bold] - Real-time dashboard
• [bold]suggestions[/bold] - Smart recommendations
• [bold]exit[/bold] - Quit agent

[success]✅ All systems ready - Enhanced with load balancing & token optimization[/success]""",
            title="Welcome",
            border_style="blue",
            padding=(1, 2)
        )

        self.ui.console.print(welcome_panel)

        # Initialize enhanced tools and agent
        tools = self.create_tools()
        agent_executor = self._create_enhanced_agent(tools)

        while True:
            try:
                # Modern input with context display
                self.ui.console.print("\n[bold blue]🤖 agent>[/bold blue] ", end="")
                user_input = input().strip()

                if user_input.lower() in ['exit', 'quit', 'bye']:
                    goodbye_panel = Panel(
                        "[bold green]👋 Thank you for using Advanced AI Coding Agent![/bold green]\n\n"
                        "Session Summary:\n"
                        f"• Commands executed: {len(self.context.command_history)}\n"
                        f"• Files processed: {len(self.context.active_files)}\n"
                        f"• Session duration: {(datetime.now() - datetime.now()).total_seconds():.1f}s\n\n"
                        "[dim]Happy coding! 🚀[/dim]",
                        title="Goodbye",
                        border_style="green"
                    )
                    self.ui.console.print(goodbye_panel)
                    break

                if user_input.lower() == 'help':
                    self.show_help()
                    continue

                if user_input.lower() == 'status':
                    self.show_status()
                    continue

                if user_input.lower() == 'suggestions':
                    suggestions = self.smart_code_suggestions("")
                    self.ui.console.print(Panel(suggestions, title="Smart Suggestions", border_style="cyan"))
                    continue

                if not user_input:
                    continue

                # Track command usage
                self.usage_analytics.track_command(user_input.split()[0] if user_input.split() else "unknown")

                # Process with modern UI and streaming
                with self.ui.console.status(f"[bold blue]Processing: {user_input[:50]}...[/bold blue]") as status:
                    try:
                        # Create enhanced context
                        context_info = self._create_enhanced_context(user_input)

                        # Execute with streaming response
                        result = self._execute_with_streaming(agent_executor, context_info, status)

                        if result:
                            self.ui.display_success("Task completed successfully!", result.get('output', ''))

                            # Generate and display smart suggestions
                            suggestions = self.smart_code_suggestions(user_input)
                            if suggestions and "💡" in suggestions:
                                self.ui.console.print(Panel(suggestions, title="Next Steps", border_style="cyan"))

                    except Exception as e:
                        self.error_tracker.track_error(str(e), user_input)
                        self.ui.display_error(f"Execution error: {str(e)}")

                        # Provide intelligent error recovery
                        fix_suggestion = self.fix_errors(str(e))
                        if fix_suggestion:
                            self.ui.console.print(Panel(fix_suggestion, title="Recovery Suggestions", border_style="yellow"))

            except KeyboardInterrupt:
                self.ui.console.print("\n[yellow]⏸️ Interrupted. Type 'exit' to quit or continue with new command.[/yellow]")
                continue
            except Exception as e:
                self.error_tracker.track_error(str(e), "main_loop")
                self.ui.display_error(f"Unexpected error: {str(e)}")
                self.ui.console.print("[info]🔄 Agent recovering... Please try again.[/info]")
                continue

    def _create_enhanced_agent(self, tools):
        """Create enhanced agent with optimized settings"""
        prompt_template = self.create_agent_prompt() + """

TOOLS:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""

        prompt = PromptTemplate(
            input_variables=["input", "agent_scratchpad", "tools", "tool_names"],
            template=prompt_template
        )

        # Create enhanced agent with optimized LLM
        agent = create_react_agent(self.llm_manager.current_model, tools, prompt)
        return AgentExecutor(
            agent=agent,
            tools=tools,
            memory=self.memory,
            verbose=False,  # Disable verbose for cleaner UI
            handle_parsing_errors=True,
            max_iterations=20,  # Increased for complex tasks
            early_stopping_method="generate"
        )

    def _create_enhanced_context(self, user_input: str) -> str:
        """Create enhanced context for better AI responses"""
        project_type = self.package_manager.detect_project_type()
        git_status = self.git_manager.get_git_status()

        return f"""
🏠 Current Context:
- Directory: {Path(self.context.current_directory).name}
- Project Type: {project_type.title()}
- Active Files: {", ".join([Path(f).name for f in self.context.active_files[-3:]]) if self.context.active_files else "None"}
- Git Branch: {git_status.get('current_branch', 'No Git')}
- Recent Commands: {", ".join(self.context.command_history[-2:]) if self.context.command_history else "None"}

🎯 User Request: {user_input}

💡 Enhanced Capabilities Available:
- Real-time streaming responses with load balancing
- Advanced security scanning and performance profiling
- Cross-language code conversion and optimization
- Intelligent project analysis and recommendations
- Smart file management with predictive caching
- Comprehensive documentation generation
- Multi-step automated pipelines
"""

    def _execute_with_streaming(self, agent_executor, context_info: str, status):
        """Execute agent with streaming response"""
        try:
            # Update status
            status.update("[bold blue]Analyzing request...[/bold blue]")

            # Execute agent
            result = agent_executor.invoke({"input": context_info})

            # Update performance metrics
            self.performance_monitor.metrics['api_calls'] += 1

            return result

        except Exception as e:
            self.performance_monitor.metrics['error_count'] += 1
            raise e

    def create_tools(self):
        """Create LangChain tools from agent methods"""
        return [
            # Core Tools
            Tool(
                name="run_command",
                description="Execute PowerShell/terminal commands with advanced error handling",
                func=lambda cmd: self.run_command(cmd)
            ),
            Tool(
                name="write_file",
                description="Write files with backup and validation. Format: path|content",
                func=lambda args: self.write_file(args.split("|")[0], "|".join(args.split("|")[1:]))
            ),
            Tool(
                name="read_file",
                description="Read files with line range support",
                func=lambda path: self.read_file(path)
            ),
            Tool(
                name="search_files",
                description="Search for files and content with pattern matching",
                func=lambda pattern: self.search_files(pattern)
            ),

            # Enhanced Web and Information Tools
            Tool(
                name="enhanced_web_search",
                description="Enhanced web search with Stack Overflow, GitHub, and documentation",
                func=lambda query: self.enhanced_web_search(query)
            ),
            Tool(
                name="get_web_info",
                description="Basic web information retrieval",
                func=lambda query: self.get_web_info(query)
            ),

            # Advanced Code Analysis Tools
            Tool(
                name="analyze_code",
                description="Deep code analysis with security and performance checks",
                func=lambda code: self.analyze_code(code)
            ),
            Tool(
                name="security_audit",
                description="Perform security audit on code. Format: code|language",
                func=lambda args: self.security_audit(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),
            Tool(
                name="performance_profile",
                description="Profile code performance. Format: code|language",
                func=lambda args: self.performance_profile(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),

            # Code Generation and Refactoring Tools
            Tool(
                name="generate_code",
                description="AI-powered code generation",
                func=lambda desc: self.generate_code(desc)
            ),
            Tool(
                name="refactor_code",
                description="AI-powered code refactoring",
                func=lambda code: self.refactor_code(code)
            ),
            Tool(
                name="cross_language_convert",
                description="Convert code between languages. Format: code|from_lang|to_lang",
                func=lambda args: self.cross_language_convert(*args.split("|"))
            ),

            # Multi-Step Pipeline Tools
            Tool(
                name="multi_step_pipeline",
                description="Execute complete code-run-fix-refactor pipeline. Format: description|language",
                func=lambda args: self.multi_step_code_pipeline(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),
            Tool(
                name="smart_suggestions",
                description="Generate smart code suggestions based on context",
                func=lambda context: self.smart_code_suggestions(context)
            ),

            # Error Handling and Debugging Tools
            Tool(
                name="fix_errors",
                description="Advanced error analysis and fixing suggestions",
                func=lambda error: self.fix_errors(error)
            ),

            # Git Integration Tools
            Tool(
                name="git_status",
                description="Get Git repository status",
                func=lambda _: json.dumps(self.git_manager.get_git_status(), indent=2)
            ),
            Tool(
                name="git_operation",
                description="Perform Git operations. Format: operation|args (e.g., 'commit|Initial commit')",
                func=lambda args: self.git_operations(args.split("|")[0], args.split("|")[1] if "|" in args else "")
            ),
            Tool(
                name="auto_commit_push",
                description="Automatically commit and push changes",
                func=lambda message: self.git_manager.auto_commit_and_push(message if message else "Auto-commit by AI Agent")
            ),

            # Package Management Tools
            Tool(
                name="install_package",
                description="Install a package using appropriate package manager",
                func=lambda package: self.package_manager.install_package(package)
            ),
            Tool(
                name="package_operation",
                description="Manage packages. Format: action|package (e.g., 'install|requests')",
                func=lambda args: self.package_operations(args.split("|")[0], args.split("|")[1] if "|" in args else "")
            ),
            Tool(
                name="detect_project_type",
                description="Auto-detect project type based on files",
                func=lambda _: self.package_manager.detect_project_type()
            ),

            # System and Project Tools
            Tool(
                name="get_project_structure",
                description="Get comprehensive project structure",
                func=lambda dir: self.get_project_structure(dir if dir else ".")
            ),
            Tool(
                name="get_system_info",
                description="Get comprehensive system information including Git and project status",
                func=lambda _: self.get_system_info()
            ),
            Tool(
                name="run_tests",
                description="Run tests with auto-detection",
                func=lambda path: self.run_tests(path if path else ".")
            )
        ]

    def create_agent_prompt(self):
        """Create comprehensive system prompt for the agent"""
        return """You are an ADVANCED AUTONOMOUS CLI CODING AGENT powered by Gemini AI with ENTERPRISE-LEVEL capabilities.

🎯 CORE CAPABILITIES:
- Full-stack development (Python, JavaScript, TypeScript, React, Node.js, Rust, Go, Java, C++, etc.)
- Advanced file operations and project management
- Terminal command execution with PowerShell support
- Deep code analysis with security and performance auditing
- Cross-language code conversion and translation
- Error detection and autonomous fixing
- Enhanced web information retrieval with multiple sources
- Multi-threaded task execution with predictive prefetching
- Context-aware decision making with smart suggestions

🧠 ADVANCED INTELLIGENCE FEATURES:
- Natural language processing for user commands (English/Hindi)
- Predictive prefetching of likely next actions with background processing
- Chain-of-thought reasoning for complex problems
- Self-critique and optimization with continuous improvement
- Context compression and smart suggestions based on patterns
- Autonomous debugging and error resolution with auto-fixing
- Cross-language integration and code translation
- Performance profiling and security auditing
- Multi-step code pipeline (Generate → Run → Fix → Refactor → Optimize)

🔄 ENHANCED WORKFLOW PROCESS:
1. ANALYZE: Deep understanding of user input and comprehensive context analysis
2. PREDICT: Background prediction of next likely actions and suggestions
3. PLAN: Create detailed step-by-step execution plan with alternatives
4. EXECUTE: Perform one action at a time with real-time monitoring
5. OBSERVE: Analyze results, performance, and security implications
6. ADAPT: Adjust plan based on observations and learned patterns
7. OPTIMIZE: Continuous improvement and refactoring suggestions
8. CONTINUE: Iterate until optimal solution is achieved

RESPOND WITH NATURAL LANGUAGE - NO JSON FORMAT REQUIRED.
Be conversational, helpful, and demonstrate your advanced enterprise capabilities.
Always explain what you're doing, why you're doing it, and what the expected outcome is.
Provide intelligent suggestions and anticipate user needs based on context."""

    def smart_code_suggestions(self, context: str) -> str:
        """Generate smart code suggestions based on context"""
        try:
            suggestions = []

            # Get predictive suggestions
            predictions = self.context.predictive_cache.next_actions
            if predictions:
                suggestions.extend([f"🔮 Predicted: {pred}" for pred in predictions[:3]])

            # Analyze current context
            if self.context.active_files:
                latest_file = self.context.active_files[-1]
                if latest_file.endswith('.py'):
                    suggestions.extend([
                        "🐍 Add type hints to functions",
                        "🧪 Generate unit tests",
                        "📝 Add docstrings",
                        "🔧 Run linting (flake8/black)"
                    ])
                elif latest_file.endswith(('.js', '.ts')):
                    suggestions.extend([
                        "⚡ Add TypeScript types",
                        "🧪 Add Jest tests",
                        "📦 Check npm dependencies",
                        "🔧 Run ESLint"
                    ])

            # Git-based suggestions
            git_status = self.git_manager.get_git_status()
            if git_status.get('has_changes'):
                suggestions.append("📝 Commit and push changes")

            if suggestions:
                return "💡 Smart Suggestions:\n" + "\n".join([f"  • {s}" for s in suggestions])
            else:
                return "✅ No specific suggestions at the moment"

        except Exception as e:
            return f"❌ Error generating suggestions: {str(e)}"

    def multi_step_code_pipeline(self, description: str, language: str = "python") -> str:
        """Execute complete code-run-fix-refactor pipeline"""
        try:
            pipeline_results = []

            # Step 1: Generate Code
            pipeline_results.append("🔄 Step 1: Generating code...")
            code_result = self.generate_code(description, language)
            pipeline_results.append(code_result)

            # Extract generated code
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', code_result, re.DOTALL)
            if not code_match:
                return "❌ Failed to extract generated code"

            generated_code = code_match.group(1)

            # Step 2: Analyze Code
            pipeline_results.append("\n🔄 Step 2: Analyzing code...")
            analysis = self.analyze_code(generated_code, language)
            pipeline_results.append(f"📊 Analysis: {analysis}")

            # Step 3: Write and Test Code
            pipeline_results.append("\n🔄 Step 3: Writing and testing code...")
            filename = f"generated_{int(time.time())}.{language}"
            write_result = self.write_file(filename, generated_code)
            pipeline_results.append(write_result)

            # Step 4: Run Code (if Python)
            if language == "python":
                pipeline_results.append("\n🔄 Step 4: Running code...")
                run_result = self.run_command(f"python {filename}")
                pipeline_results.append(run_result)

                # Step 5: Fix errors if any
                if "❌" in run_result:
                    pipeline_results.append("\n🔄 Step 5: Fixing errors...")
                    fix_result = self.fix_errors(run_result, generated_code)
                    pipeline_results.append(fix_result)

            # Step 6: Refactor Code
            pipeline_results.append("\n🔄 Step 6: Refactoring code...")
            refactor_result = self.refactor_code(generated_code)
            pipeline_results.append(refactor_result)

            return "\n".join(pipeline_results)

        except Exception as e:
            return f"❌ Pipeline error: {str(e)}"

    def cross_language_convert(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        return self.language_converter.convert_code(code, from_lang, to_lang)

    def enhanced_web_search(self, query: str) -> str:
        """Enhanced web search with multiple sources"""
        context = " ".join(self.context.command_history[-3:]) if self.context.command_history else ""
        return self.web_scraper.enhanced_web_search(query, context)

    def git_operations(self, operation: str, args: str = "") -> str:
        """Perform Git operations"""
        return self.git_manager.git_operation(operation, args)

    def package_operations(self, action: str, package: str = "") -> str:
        """Manage packages and dependencies"""
        return self.package_manager.manage_dependencies(action, package)

    def performance_profile(self, code: str, language: str = "python") -> str:
        """Profile code performance"""
        try:
            if language.lower() != "python":
                return "❌ Performance profiling currently only supports Python"

            # Create temporary file
            temp_file = f"profile_temp_{int(time.time())}.py"

            # Add profiling code
            profiled_code = f"""
import cProfile
import pstats
import io

def profile_target():
{chr(10).join(['    ' + line for line in code.split(chr(10))])}

if __name__ == "__main__":
    pr = cProfile.Profile()
    pr.enable()
    profile_target()
    pr.disable()

    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(10)
    print(s.getvalue())
"""

            # Write and run profiled code
            self.write_file(temp_file, profiled_code)
            result = self.run_command(f"python {temp_file}")

            # Clean up
            if os.path.exists(temp_file):
                os.remove(temp_file)

            return f"⚡ Performance Profile:\n{result}"

        except Exception as e:
            return f"❌ Performance profiling error: {str(e)}"

    def security_audit(self, code: str, language: str = "python") -> str:
        """Perform security audit on code"""
        try:
            analysis = self.code_analyzer.deep_analyze_code(code, language)

            if hasattr(analysis, 'security_issues') and analysis.security_issues:
                issues = "\n".join([f"  ⚠️ {issue}" for issue in analysis.security_issues])
                return f"🔒 Security Audit Results:\n{issues}\n\n💡 Recommendations:\n  • Use parameterized queries\n  • Validate all inputs\n  • Avoid hardcoded secrets"
            else:
                return "✅ No obvious security issues detected"

        except Exception as e:
            return f"❌ Security audit error: {str(e)}"

    def get_system_info(self) -> str:
        """Get comprehensive system information"""
        try:
            import platform
            import psutil

            # Get git status
            git_status = self.git_manager.get_git_status()

            # Get project type
            project_type = self.package_manager.detect_project_type()

            info = {
                "os": platform.system(),
                "version": platform.version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "current_dir": os.getcwd(),
                "user": os.getenv("USERNAME") or os.getenv("USER", "unknown"),
                "memory_usage": f"{psutil.virtual_memory().percent}%",
                "cpu_usage": f"{psutil.cpu_percent()}%",
                "git_status": git_status,
                "project_type": project_type,
                "active_files": len(self.context.active_files),
                "command_history": len(self.context.command_history)
            }
            return json.dumps(info, indent=2)
        except Exception as e:
            return f"Error getting system info: {str(e)}"

class AdvancedCodeAnalyzer:
    def __init__(self):
        self.language_parsers = {}
        self.security_patterns = {
            'sql_injection': [r'SELECT.*FROM.*WHERE.*=.*\+', r'INSERT.*VALUES.*\+'],
            'xss': [r'innerHTML.*\+', r'document\.write.*\+'],
            'path_traversal': [r'\.\./', r'\.\.\\'],
            'hardcoded_secrets': [r'password\s*=\s*["\'][^"\']+["\']', r'api_key\s*=\s*["\'][^"\']+["\']']
        }

    def deep_analyze_code(self, code: str, language: str = "python") -> CodeAnalysisResult:
        """Perform deep code analysis with security and performance checks"""
        result = CodeAnalysisResult()

        try:
            if language.lower() == "python":
                result = self._analyze_python_code(code)
            elif language.lower() in ["javascript", "typescript"]:
                result = self._analyze_js_code(code)
            else:
                result = self._analyze_generic_code(code, language)

            # Add security analysis
            result.security_issues = self._detect_security_issues(code)

            # Add performance analysis
            result.performance_issues = self._detect_performance_issues(code, language)

            # Generate refactoring suggestions
            result.refactor_suggestions = self._generate_refactor_suggestions(code, language)

        except Exception as e:
            logging.error(f"Code analysis error: {e}")

        return result

    def _analyze_python_code(self, code: str) -> CodeAnalysisResult:
        """Analyze Python code using AST"""
        result = CodeAnalysisResult()

        try:
            tree = ast.parse(code)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    result.functions.append(node.name)
                    # Calculate complexity
                    result.complexity += self._calculate_complexity(node)
                elif isinstance(node, ast.ClassDef):
                    result.classes.append(node.name)
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        result.imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        result.imports.append(f"from {node.module}")

            # Detect code duplicates
            result.duplicates = self._detect_duplicates(code)

        except SyntaxError as e:
            result.security_issues.append(f"Syntax Error: {str(e)}")

        return result

    def _analyze_js_code(self, code: str) -> CodeAnalysisResult:
        """Analyze JavaScript/TypeScript code"""
        result = CodeAnalysisResult()

        # Basic regex-based analysis for JS
        function_pattern = r'function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=>\s*'
        class_pattern = r'class\s+(\w+)'
        import_pattern = r'import.*from\s+["\']([^"\']+)["\']|require\(["\']([^"\']+)["\']\)'

        functions = re.findall(function_pattern, code)
        classes = re.findall(class_pattern, code)
        imports = re.findall(import_pattern, code)

        result.functions = [f[0] or f[1] or f[2] for f in functions if any(f)]
        result.classes = classes
        result.imports = [i[0] or i[1] for i in imports if any(i)]
        result.complexity = len(result.functions) * 2 + len(result.classes) * 3

        return result

    def _analyze_generic_code(self, code: str, language: str) -> CodeAnalysisResult:
        """Generic code analysis for other languages"""
        result = CodeAnalysisResult()

        lines = code.split('\n')
        result.complexity = len([line for line in lines if line.strip() and not line.strip().startswith('#')])

        return result

    def _calculate_complexity(self, node) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
        return complexity

    def _detect_duplicates(self, code: str) -> List[Dict]:
        """Detect code duplicates"""
        lines = code.split('\n')
        duplicates = []

        for i, line1 in enumerate(lines):
            if len(line1.strip()) < 10:  # Skip short lines
                continue
            for j, line2 in enumerate(lines[i+1:], i+1):
                if line1.strip() == line2.strip():
                    duplicates.append({
                        'line1': i+1,
                        'line2': j+1,
                        'content': line1.strip()
                    })

        return duplicates

    def _detect_security_issues(self, code: str) -> List[str]:
        """Detect security vulnerabilities"""
        issues = []

        for issue_type, patterns in self.security_patterns.items():
            for pattern in patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    issues.append(f"Potential {issue_type.replace('_', ' ')} vulnerability detected")

        return issues

    def _detect_performance_issues(self, code: str, language: str) -> List[str]:
        """Detect performance issues"""
        issues = []

        if language.lower() == "python":
            # Check for common Python performance issues
            if re.search(r'for.*in.*range\(len\(', code):
                issues.append("Use enumerate() instead of range(len()) for better performance")
            if re.search(r'\+.*str\(', code):
                issues.append("Consider using f-strings for string formatting")
            if re.search(r'\.append\(.*\)\s*\n.*\.append\(', code):
                issues.append("Consider using list comprehension for multiple appends")

        elif language.lower() in ["javascript", "typescript"]:
            if re.search(r'document\.getElementById', code):
                issues.append("Consider caching DOM elements for better performance")
            if re.search(r'for\s*\(.*\.length', code):
                issues.append("Cache array length in for loops")

        return issues

    def _generate_refactor_suggestions(self, code: str, language: str) -> List[str]:
        """Generate refactoring suggestions"""
        suggestions = []

        lines = code.split('\n')
        if len(lines) > 50:
            suggestions.append("Consider breaking this into smaller functions")

        if language.lower() == "python":
            if re.search(r'def\s+\w+.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n', code):
                suggestions.append("Function is too long, consider extracting smaller functions")
            if code.count('if') > 5:
                suggestions.append("Consider using polymorphism or strategy pattern for complex conditionals")

        return suggestions

class LanguageConverter:
    def __init__(self):
        self.conversion_templates = {
            'python_to_javascript': {
                'print': 'console.log',
                'def ': 'function ',
                'True': 'true',
                'False': 'false',
                'None': 'null',
                'elif': 'else if',
                'and': '&&',
                'or': '||',
                'not': '!',
                'len(': '.length',
                'range(': 'Array.from({length: ',
                'str(': 'String(',
                'int(': 'parseInt(',
                'float(': 'parseFloat('
            },
            'javascript_to_python': {
                'console.log': 'print',
                'function ': 'def ',
                'true': 'True',
                'false': 'False',
                'null': 'None',
                'else if': 'elif',
                '&&': 'and',
                '||': 'or',
                '!': 'not ',
                '.length': 'len(',
                'parseInt(': 'int(',
                'parseFloat(': 'float(',
                'String(': 'str('
            }
        }

    def convert_code(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        try:
            conversion_key = f"{from_lang.lower()}_to_{to_lang.lower()}"

            if conversion_key in self.conversion_templates:
                converted_code = code
                templates = self.conversion_templates[conversion_key]

                for old_syntax, new_syntax in templates.items():
                    converted_code = converted_code.replace(old_syntax, new_syntax)

                # Language-specific formatting
                if to_lang.lower() == 'python':
                    converted_code = self._format_for_python(converted_code)
                elif to_lang.lower() == 'javascript':
                    converted_code = self._format_for_javascript(converted_code)

                return f"🔄 Converted from {from_lang} to {to_lang}:\n```{to_lang.lower()}\n{converted_code}\n```"
            else:
                return f"❌ Conversion from {from_lang} to {to_lang} not yet supported"

        except Exception as e:
            return f"❌ Error converting code: {str(e)}"

    def _format_for_python(self, code: str) -> str:
        """Format code for Python syntax"""
        lines = code.split('\n')
        formatted_lines = []

        for line in lines:
            # Remove semicolons
            line = line.rstrip(';')
            # Fix indentation (basic)
            if line.strip().endswith(':'):
                formatted_lines.append(line)
            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def _format_for_javascript(self, code: str) -> str:
        """Format code for JavaScript syntax"""
        lines = code.split('\n')
        formatted_lines = []

        for line in lines:
            # Add semicolons
            if line.strip() and not line.strip().endswith((';', '{', '}')):
                line = line + ';'
            formatted_lines.append(line)

        return '\n'.join(formatted_lines)

class RefactoringEngine:
    def __init__(self):
        self.refactoring_patterns = {
            'extract_function': self._extract_function,
            'remove_duplicates': self._remove_duplicates,
            'optimize_imports': self._optimize_imports,
            'improve_naming': self._improve_naming,
            'add_type_hints': self._add_type_hints
        }

    def auto_refactor(self, code: str, language: str = "python") -> str:
        """Automatically refactor code for better quality"""
        try:
            refactored_code = code
            suggestions = []

            # Apply all refactoring patterns
            for pattern_name, pattern_func in self.refactoring_patterns.items():
                try:
                    result = pattern_func(refactored_code, language)
                    if result != refactored_code:
                        refactored_code = result
                        suggestions.append(f"Applied {pattern_name.replace('_', ' ')}")
                except Exception as e:
                    logging.error(f"Refactoring pattern {pattern_name} failed: {e}")

            if suggestions:
                return f"🔧 Refactored code:\n```{language}\n{refactored_code}\n```\n\n✅ Applied: {', '.join(suggestions)}"
            else:
                return f"✅ Code is already well-structured, no refactoring needed."

        except Exception as e:
            return f"❌ Error during refactoring: {str(e)}"

    def _extract_function(self, code: str, language: str) -> str:
        """Extract long functions into smaller ones"""
        if language.lower() != "python":
            return code

        lines = code.split('\n')
        refactored_lines = []
        current_function = []
        in_function = False
        function_indent = 0

        for line in lines:
            if line.strip().startswith('def ') and ':' in line:
                if current_function and len(current_function) > 20:
                    # Extract helper function
                    helper_func = self._create_helper_function(current_function)
                    refactored_lines.extend(helper_func)

                current_function = [line]
                in_function = True
                function_indent = len(line) - len(line.lstrip())
            elif in_function:
                if line.strip() and len(line) - len(line.lstrip()) <= function_indent and not line.startswith(' '):
                    in_function = False
                    refactored_lines.extend(current_function)
                    refactored_lines.append(line)
                    current_function = []
                else:
                    current_function.append(line)
            else:
                refactored_lines.append(line)

        if current_function:
            refactored_lines.extend(current_function)

        return '\n'.join(refactored_lines)

    def _create_helper_function(self, function_lines: List[str]) -> List[str]:
        """Create a helper function from code block"""
        # Simple helper function extraction
        helper_lines = []
        helper_lines.append("def helper_function():")
        helper_lines.append("    # Extracted helper function")
        for line in function_lines[10:15]:  # Extract middle part
            helper_lines.append("    " + line.strip())
        helper_lines.append("")
        return helper_lines

    def _remove_duplicates(self, code: str, language: str) -> str:
        """Remove duplicate code blocks"""
        lines = code.split('\n')
        seen_lines = set()
        unique_lines = []

        for line in lines:
            if line.strip() and line.strip() not in seen_lines:
                unique_lines.append(line)
                seen_lines.add(line.strip())
            elif not line.strip():  # Keep empty lines
                unique_lines.append(line)

        return '\n'.join(unique_lines)

    def _optimize_imports(self, code: str, language: str) -> str:
        """Optimize import statements"""
        if language.lower() != "python":
            return code

        lines = code.split('\n')
        imports = []
        other_lines = []

        for line in lines:
            if line.strip().startswith(('import ', 'from ')):
                imports.append(line)
            else:
                other_lines.append(line)

        # Sort and deduplicate imports
        unique_imports = list(set(imports))
        unique_imports.sort()

        # Combine imports and other code
        result = unique_imports + [''] + other_lines
        return '\n'.join(result)

    def _improve_naming(self, code: str, language: str) -> str:
        """Improve variable and function naming"""
        # Basic naming improvements
        improvements = {
            'temp': 'temporary_value',
            'tmp': 'temporary',
            'i': 'index',
            'j': 'inner_index',
            'x': 'value',
            'y': 'result',
            'data': 'input_data',
            'result': 'output_result'
        }

        improved_code = code
        for old_name, new_name in improvements.items():
            # Only replace standalone variables, not parts of words
            pattern = r'\b' + re.escape(old_name) + r'\b'
            improved_code = re.sub(pattern, new_name, improved_code)

        return improved_code

    def _add_type_hints(self, code: str, language: str) -> str:
        """Add type hints to Python functions"""
        if language.lower() != "python":
            return code

        # Basic type hint addition
        lines = code.split('\n')
        typed_lines = []

        for line in lines:
            if line.strip().startswith('def ') and '(' in line and '->' not in line:
                # Add basic return type hint
                if ':' in line:
                    line = line.replace(':', ' -> Any:')
            typed_lines.append(line)

        return '\n'.join(typed_lines)

class EnhancedWebScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.documentation_sources = {
            'python': 'https://docs.python.org/3/',
            'javascript': 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
            'react': 'https://reactjs.org/docs/',
            'node': 'https://nodejs.org/en/docs/',
            'typescript': 'https://www.typescriptlang.org/docs/'
        }

    def enhanced_web_search(self, query: str, context: str = "") -> str:
        """Enhanced web information retrieval with context awareness"""
        try:
            results = []

            # Search multiple sources
            sources = [
                self._search_stackoverflow(query),
                self._search_github(query),
                self._search_documentation(query, context)
            ]

            for source_result in sources:
                if source_result:
                    results.append(source_result)

            if results:
                return f"🌐 Enhanced Web Search Results for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ No relevant information found for '{query}'"

        except Exception as e:
            return f"❌ Error during web search: {str(e)}"

    def _search_stackoverflow(self, query: str) -> str:
        """Search Stack Overflow for solutions"""
        try:
            # Use Stack Exchange API
            api_url = f"https://api.stackexchange.com/2.3/search/advanced"
            params = {
                'order': 'desc',
                'sort': 'relevance',
                'q': query,
                'site': 'stackoverflow',
                'pagesize': 3
            }

            response = self.session.get(api_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('items', []):
                    title = item.get('title', 'No title')
                    link = item.get('link', '')
                    score = item.get('score', 0)
                    results.append(f"📝 {title} (Score: {score})\n   {link}")

                return "🔍 Stack Overflow Results:\n" + "\n".join(results)

        except Exception as e:
            logging.error(f"Stack Overflow search error: {e}")

        return ""

    def _search_github(self, query: str) -> str:
        """Search GitHub for code examples"""
        try:
            # GitHub search API
            api_url = "https://api.github.com/search/repositories"
            params = {
                'q': query,
                'sort': 'stars',
                'order': 'desc',
                'per_page': 3
            }

            response = self.session.get(api_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('items', []):
                    name = item.get('full_name', 'Unknown')
                    description = item.get('description', 'No description')
                    stars = item.get('stargazers_count', 0)
                    url = item.get('html_url', '')
                    results.append(f"⭐ {name} ({stars} stars)\n   {description}\n   {url}")

                return "🐙 GitHub Results:\n" + "\n".join(results)

        except Exception as e:
            logging.error(f"GitHub search error: {e}")

        return ""

    def _search_documentation(self, query: str, context: str) -> str:
        """Search official documentation"""
        try:
            # Determine language from context
            language = self._detect_language(context)

            if language in self.documentation_sources:
                base_url = self.documentation_sources[language]
                search_url = f"{base_url}search.html?q={urllib.parse.quote(query)}"

                response = self.session.get(search_url, timeout=10)
                if response.status_code == 200:
                    return f"📚 Official {language.title()} Documentation:\n   {search_url}"

        except Exception as e:
            logging.error(f"Documentation search error: {e}")

        return ""

    def _detect_language(self, context: str) -> str:
        """Detect programming language from context"""
        context_lower = context.lower()

        if any(keyword in context_lower for keyword in ['python', 'py', 'pip', 'django', 'flask']):
            return 'python'
        elif any(keyword in context_lower for keyword in ['javascript', 'js', 'node', 'npm']):
            return 'javascript'
        elif any(keyword in context_lower for keyword in ['react', 'jsx', 'component']):
            return 'react'
        elif any(keyword in context_lower for keyword in ['typescript', 'ts']):
            return 'typescript'

        return 'python'  # Default

# Advanced Capability Classes
class SmartFileManager:
    """Advanced file management with intelligent operations"""

    def __init__(self):
        self.file_cache = {}
        self.watch_list = set()

    async def smart_read(self, file_path: str, encoding: str = 'utf-8') -> str:
        """Smart file reading with caching and error handling"""
        try:
            if file_path in self.file_cache:
                cached_time, content = self.file_cache[file_path]
                file_time = os.path.getmtime(file_path)
                if file_time <= cached_time:
                    return content

            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                content = await f.read()
                self.file_cache[file_path] = (time.time(), content)
                return content

        except Exception as e:
            return f"Error reading file: {str(e)}"

    async def smart_write(self, file_path: str, content: str, backup: bool = True) -> str:
        """Smart file writing with backup and validation"""
        try:
            if backup and os.path.exists(file_path):
                backup_path = f"{file_path}.backup_{int(time.time())}"
                shutil.copy2(file_path, backup_path)

            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)

            # Update cache
            self.file_cache[file_path] = (time.time(), content)
            return f"✅ File written successfully: {file_path}"

        except Exception as e:
            return f"❌ Error writing file: {str(e)}"

class ProjectAnalyzer:
    """Advanced project analysis and insights"""

    def __init__(self):
        self.analysis_cache = {}

    def analyze_project_structure(self, directory: str = ".") -> Dict:
        """Analyze project structure and provide insights"""
        try:
            analysis = {
                'project_type': self._detect_project_type(directory),
                'languages': self._detect_languages(directory),
                'dependencies': self._analyze_dependencies(directory),
                'complexity': self._calculate_complexity(directory),
                'health_score': 0,
                'recommendations': []
            }

            # Calculate health score
            analysis['health_score'] = self._calculate_health_score(analysis)

            # Generate recommendations
            analysis['recommendations'] = self._generate_recommendations(analysis)

            return analysis

        except Exception as e:
            return {'error': str(e)}

    def _detect_project_type(self, directory: str) -> str:
        """Detect project type from files"""
        files = os.listdir(directory)

        if 'package.json' in files:
            return 'Node.js'
        elif 'requirements.txt' in files or 'setup.py' in files:
            return 'Python'
        elif 'Cargo.toml' in files:
            return 'Rust'
        elif 'pom.xml' in files:
            return 'Java'
        elif 'composer.json' in files:
            return 'PHP'
        else:
            return 'Unknown'

    def _detect_languages(self, directory: str) -> List[str]:
        """Detect programming languages used"""
        languages = set()

        for root, dirs, files in os.walk(directory):
            for file in files:
                ext = Path(file).suffix.lower()
                if ext == '.py':
                    languages.add('Python')
                elif ext in ['.js', '.jsx']:
                    languages.add('JavaScript')
                elif ext in ['.ts', '.tsx']:
                    languages.add('TypeScript')
                elif ext in ['.java']:
                    languages.add('Java')
                elif ext in ['.rs']:
                    languages.add('Rust')
                elif ext in ['.go']:
                    languages.add('Go')
                elif ext in ['.cpp', '.cc', '.cxx']:
                    languages.add('C++')
                elif ext in ['.c']:
                    languages.add('C')

        return list(languages)

    def _analyze_dependencies(self, directory: str) -> Dict:
        """Analyze project dependencies"""
        deps = {'count': 0, 'outdated': [], 'security_issues': []}

        # Check package.json
        package_json = os.path.join(directory, 'package.json')
        if os.path.exists(package_json):
            try:
                with open(package_json, 'r') as f:
                    data = json.load(f)
                    deps['count'] += len(data.get('dependencies', {}))
                    deps['count'] += len(data.get('devDependencies', {}))
            except:
                pass

        # Check requirements.txt
        requirements_txt = os.path.join(directory, 'requirements.txt')
        if os.path.exists(requirements_txt):
            try:
                with open(requirements_txt, 'r') as f:
                    deps['count'] += len([line for line in f if line.strip() and not line.startswith('#')])
            except:
                pass

        return deps

    def _calculate_complexity(self, directory: str) -> Dict:
        """Calculate project complexity metrics"""
        complexity = {
            'files': 0,
            'lines_of_code': 0,
            'functions': 0,
            'classes': 0
        }

        code_extensions = {'.py', '.js', '.ts', '.java', '.rs', '.go', '.cpp', '.c'}

        for root, dirs, files in os.walk(directory):
            for file in files:
                if Path(file).suffix.lower() in code_extensions:
                    complexity['files'] += 1
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            complexity['lines_of_code'] += len(content.split('\n'))

                            # Basic function/class counting
                            complexity['functions'] += len(re.findall(r'def\s+\w+|function\s+\w+', content))
                            complexity['classes'] += len(re.findall(r'class\s+\w+', content))
                    except:
                        pass

        return complexity

    def _calculate_health_score(self, analysis: Dict) -> int:
        """Calculate project health score (0-100)"""
        score = 100

        # Deduct points for complexity
        if analysis['complexity']['files'] > 100:
            score -= 10
        if analysis['complexity']['lines_of_code'] > 10000:
            score -= 15

        # Deduct points for dependencies
        if analysis['dependencies']['count'] > 50:
            score -= 10

        return max(0, score)

    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []

        if analysis['complexity']['files'] > 100:
            recommendations.append("Consider breaking down the project into smaller modules")

        if analysis['dependencies']['count'] > 50:
            recommendations.append("Review and optimize dependencies to reduce bloat")

        if len(analysis['languages']) > 3:
            recommendations.append("Consider standardizing on fewer programming languages")

        return recommendations

class SecurityScanner:
    """Advanced security scanning and vulnerability detection"""

    def __init__(self):
        self.vulnerability_patterns = {
            'sql_injection': [
                r'SELECT.*FROM.*WHERE.*\+',
                r'INSERT.*VALUES.*\+',
                r'UPDATE.*SET.*\+'
            ],
            'xss': [
                r'innerHTML.*\+',
                r'document\.write.*\+',
                r'eval\s*\('
            ],
            'path_traversal': [
                r'\.\./',
                r'\.\.\\'
            ],
            'hardcoded_secrets': [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'api_key\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']'
            ]
        }

    def scan_code(self, code: str, language: str = "python") -> Dict:
        """Scan code for security vulnerabilities"""
        vulnerabilities = []

        for vuln_type, patterns in self.vulnerability_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, code, re.IGNORECASE)
                for match in matches:
                    line_num = code[:match.start()].count('\n') + 1
                    vulnerabilities.append({
                        'type': vuln_type,
                        'line': line_num,
                        'code': match.group(),
                        'severity': self._get_severity(vuln_type)
                    })

        return {
            'vulnerabilities': vulnerabilities,
            'risk_score': self._calculate_risk_score(vulnerabilities),
            'recommendations': self._get_security_recommendations(vulnerabilities)
        }

    def _get_severity(self, vuln_type: str) -> str:
        """Get vulnerability severity"""
        severity_map = {
            'sql_injection': 'HIGH',
            'xss': 'MEDIUM',
            'path_traversal': 'HIGH',
            'hardcoded_secrets': 'MEDIUM'
        }
        return severity_map.get(vuln_type, 'LOW')

    def _calculate_risk_score(self, vulnerabilities: List[Dict]) -> int:
        """Calculate overall risk score"""
        score = 0
        for vuln in vulnerabilities:
            if vuln['severity'] == 'HIGH':
                score += 10
            elif vuln['severity'] == 'MEDIUM':
                score += 5
            else:
                score += 1
        return min(100, score)

    def _get_security_recommendations(self, vulnerabilities: List[Dict]) -> List[str]:
        """Get security recommendations"""
        recommendations = []

        vuln_types = set(v['type'] for v in vulnerabilities)

        if 'sql_injection' in vuln_types:
            recommendations.append("Use parameterized queries to prevent SQL injection")
        if 'xss' in vuln_types:
            recommendations.append("Sanitize user input and use safe DOM manipulation")
        if 'path_traversal' in vuln_types:
            recommendations.append("Validate and sanitize file paths")
        if 'hardcoded_secrets' in vuln_types:
            recommendations.append("Move secrets to environment variables or secure vaults")

        return recommendations

class PerformanceProfiler:
    """Advanced performance profiling and optimization"""

    def __init__(self):
        self.profiling_data = {}

    def profile_code(self, code: str, language: str = "python") -> Dict:
        """Profile code performance"""
        if language.lower() == "python":
            return self._profile_python_code(code)
        else:
            return self._basic_performance_analysis(code, language)

    def _profile_python_code(self, code: str) -> Dict:
        """Profile Python code execution"""
        try:
            # Create temporary file
            temp_file = f"profile_temp_{int(time.time())}.py"

            # Add profiling wrapper
            profiled_code = f"""
import cProfile
import pstats
import io
import time

def profile_target():
{chr(10).join(['    ' + line for line in code.split(chr(10))])}

if __name__ == "__main__":
    start_time = time.time()
    pr = cProfile.Profile()
    pr.enable()

    try:
        profile_target()
    except Exception as e:
        print(f"Execution error: {{e}}")

    pr.disable()
    end_time = time.time()

    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(10)

    print(f"Execution time: {{end_time - start_time:.4f}} seconds")
    print("Top functions by cumulative time:")
    print(s.getvalue())
"""

            with open(temp_file, 'w') as f:
                f.write(profiled_code)

            # Run profiling
            result = subprocess.run(
                ['python', temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )

            # Clean up
            if os.path.exists(temp_file):
                os.remove(temp_file)

            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr,
                'recommendations': self._generate_performance_recommendations(result.stdout)
            }

        except Exception as e:
            return {'success': False, 'error': str(e), 'recommendations': []}

    def _basic_performance_analysis(self, code: str, language: str) -> Dict:
        """Basic performance analysis for non-Python code"""
        analysis = {
            'lines': len(code.split('\n')),
            'complexity': self._estimate_complexity(code),
            'recommendations': []
        }

        # Add language-specific recommendations
        if language.lower() == 'javascript':
            if 'for(' in code and '.length' in code:
                analysis['recommendations'].append("Cache array length in for loops")
            if 'document.getElementById' in code:
                analysis['recommendations'].append("Cache DOM element references")

        return analysis

    def _estimate_complexity(self, code: str) -> str:
        """Estimate code complexity"""
        lines = len(code.split('\n'))
        if lines < 50:
            return "Low"
        elif lines < 200:
            return "Medium"
        else:
            return "High"

    def _generate_performance_recommendations(self, profile_output: str) -> List[str]:
        """Generate performance recommendations from profile output"""
        recommendations = []

        if "time.sleep" in profile_output:
            recommendations.append("Remove or optimize sleep calls")
        if "list comprehension" in profile_output.lower():
            recommendations.append("Consider using generators for large datasets")

        return recommendations

class SmartCodeGenerator:
    """Advanced AI-powered code generation"""

    def __init__(self):
        self.generation_templates = {}
        self.context_analyzer = ContextAnalyzer()

    def generate_smart_code(self, description: str, language: str = "python", context: str = "") -> str:
        """Generate code with context awareness"""
        try:
            # Analyze context for better generation
            context_info = self.context_analyzer.analyze_context(context)

            # Create enhanced prompt
            prompt = self._create_enhanced_prompt(description, language, context_info)

            # Generate code using LLM
            response = llm_manager.get_optimized_response([HumanMessage(content=prompt)])

            # Post-process generated code
            generated_code = self._post_process_code(response.content, language)

            return generated_code

        except Exception as e:
            return f"❌ Code generation error: {str(e)}"

    def _create_enhanced_prompt(self, description: str, language: str, context_info: Dict) -> str:
        """Create enhanced prompt with context"""
        prompt = f"""Generate {language} code for: {description}

Context Information:
- Project Type: {context_info.get('project_type', 'Unknown')}
- Existing Libraries: {', '.join(context_info.get('libraries', []))}
- Code Style: {context_info.get('style', 'Standard')}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comprehensive comments
- Follow {language} best practices
- Make it modular and reusable
- Include type hints (if applicable)
- Add docstrings for functions/classes

Code:"""
        return prompt

    def _post_process_code(self, code: str, language: str) -> str:
        """Post-process generated code"""
        # Extract code from markdown if present
        code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', code, re.DOTALL)
        if code_match:
            code = code_match.group(1)

        # Add language-specific improvements
        if language.lower() == 'python':
            code = self._improve_python_code(code)
        elif language.lower() in ['javascript', 'typescript']:
            code = self._improve_js_code(code)

        return code

    def _improve_python_code(self, code: str) -> str:
        """Improve Python code quality"""
        lines = code.split('\n')
        improved_lines = []

        for line in lines:
            # Add type hints to function definitions
            if line.strip().startswith('def ') and '->' not in line and ':' in line:
                line = line.replace(':', ' -> Any:')
            improved_lines.append(line)

        return '\n'.join(improved_lines)

    def _improve_js_code(self, code: str) -> str:
        """Improve JavaScript code quality"""
        lines = code.split('\n')
        improved_lines = []

        for line in lines:
            # Add semicolons if missing
            if line.strip() and not line.strip().endswith((';', '{', '}')):
                line = line + ';'
            improved_lines.append(line)

        return '\n'.join(improved_lines)

class ContextAnalyzer:
    """Analyze code context for better generation"""

    def analyze_context(self, context: str) -> Dict:
        """Analyze context and extract useful information"""
        info = {
            'project_type': 'Unknown',
            'libraries': [],
            'style': 'Standard'
        }

        # Detect project type
        if 'package.json' in context or 'npm' in context:
            info['project_type'] = 'Node.js'
        elif 'requirements.txt' in context or 'pip' in context:
            info['project_type'] = 'Python'

        # Extract libraries
        import_patterns = [
            r'import\s+(\w+)',
            r'from\s+(\w+)',
            r'require\(["\']([^"\']+)["\']\)'
        ]

        for pattern in import_patterns:
            matches = re.findall(pattern, context)
            info['libraries'].extend(matches)

        return info

class IntelligentTestRunner:
    """Advanced test running and analysis"""

    def __init__(self):
        self.test_frameworks = {
            'python': ['pytest', 'unittest', 'nose2'],
            'javascript': ['jest', 'mocha', 'jasmine'],
            'java': ['junit', 'testng'],
            'rust': ['cargo test']
        }

    def run_smart_tests(self, directory: str = ".", language: str = None) -> Dict:
        """Run tests with intelligent framework detection"""
        if not language:
            language = self._detect_language(directory)

        framework = self._detect_test_framework(directory, language)

        if framework:
            return self._run_tests_with_framework(framework, directory)
        else:
            return {'error': 'No test framework detected'}

    def _detect_language(self, directory: str) -> str:
        """Detect primary language in directory"""
        extensions = defaultdict(int)

        for root, dirs, files in os.walk(directory):
            for file in files:
                ext = Path(file).suffix.lower()
                if ext in ['.py', '.js', '.java', '.rs']:
                    extensions[ext] += 1

        if not extensions:
            return 'unknown'

        most_common_ext = max(extensions, key=extensions.get)
        ext_to_lang = {'.py': 'python', '.js': 'javascript', '.java': 'java', '.rs': 'rust'}
        return ext_to_lang.get(most_common_ext, 'unknown')

    def _detect_test_framework(self, directory: str, language: str) -> str:
        """Detect test framework being used"""
        if language == 'python':
            if os.path.exists(os.path.join(directory, 'pytest.ini')) or 'pytest' in os.listdir(directory):
                return 'pytest'
            elif any('test_' in f for f in os.listdir(directory)):
                return 'unittest'
        elif language == 'javascript':
            package_json = os.path.join(directory, 'package.json')
            if os.path.exists(package_json):
                try:
                    with open(package_json, 'r') as f:
                        data = json.load(f)
                        deps = {**data.get('dependencies', {}), **data.get('devDependencies', {})}
                        if 'jest' in deps:
                            return 'jest'
                        elif 'mocha' in deps:
                            return 'mocha'
                except:
                    pass

        return None

    def _run_tests_with_framework(self, framework: str, directory: str) -> Dict:
        """Run tests with specific framework"""
        commands = {
            'pytest': ['python', '-m', 'pytest', '-v'],
            'unittest': ['python', '-m', 'unittest', 'discover'],
            'jest': ['npm', 'test'],
            'mocha': ['npx', 'mocha']
        }

        command = commands.get(framework, ['echo', 'Unknown framework'])

        try:
            result = subprocess.run(
                command,
                cwd=directory,
                capture_output=True,
                text=True,
                timeout=300
            )

            return {
                'framework': framework,
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr,
                'analysis': self._analyze_test_results(result.stdout, framework)
            }

        except Exception as e:
            return {'error': str(e)}

    def _analyze_test_results(self, output: str, framework: str) -> Dict:
        """Analyze test results and provide insights"""
        analysis = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'coverage': 0,
            'recommendations': []
        }

        if framework == 'pytest':
            # Parse pytest output
            passed_match = re.search(r'(\d+) passed', output)
            failed_match = re.search(r'(\d+) failed', output)

            if passed_match:
                analysis['passed'] = int(passed_match.group(1))
            if failed_match:
                analysis['failed'] = int(failed_match.group(1))

            analysis['total_tests'] = analysis['passed'] + analysis['failed']

        # Generate recommendations
        if analysis['failed'] > 0:
            analysis['recommendations'].append("Fix failing tests before deployment")
        if analysis['total_tests'] < 10:
            analysis['recommendations'].append("Consider adding more test coverage")

        return analysis

class DocumentationGenerator:
    """Advanced documentation generation"""

    def __init__(self):
        self.doc_templates = {}

    def generate_documentation(self, code: str, language: str = "python") -> str:
        """Generate comprehensive documentation"""
        try:
            if language.lower() == 'python':
                return self._generate_python_docs(code)
            elif language.lower() in ['javascript', 'typescript']:
                return self._generate_js_docs(code)
            else:
                return self._generate_generic_docs(code, language)
        except Exception as e:
            return f"❌ Documentation generation error: {str(e)}"

    def _generate_python_docs(self, code: str) -> str:
        """Generate Python documentation"""
        try:
            tree = ast.parse(code)
            docs = []

            docs.append("# Code Documentation\n")

            # Extract classes and functions
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    docs.append(f"## Class: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")

                    # Methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            docs.append(f"### Method: {item.name}\n")
                            if ast.get_docstring(item):
                                docs.append(f"{ast.get_docstring(item)}\n")

                elif isinstance(node, ast.FunctionDef) and not hasattr(node, 'parent_class'):
                    docs.append(f"## Function: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")

            return "\n".join(docs)

        except Exception as e:
            return f"Error generating Python docs: {str(e)}"

    def _generate_js_docs(self, code: str) -> str:
        """Generate JavaScript documentation"""
        docs = ["# Code Documentation\n"]

        # Extract functions
        function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
        functions = re.findall(function_pattern, code)

        for func in functions:
            docs.append(f"## Function: {func}\n")
            docs.append("Description: [Add description]\n")

        # Extract classes
        class_pattern = r'class\s+(\w+)\s*\{'
        classes = re.findall(class_pattern, code)

        for cls in classes:
            docs.append(f"## Class: {cls}\n")
            docs.append("Description: [Add description]\n")

        return "\n".join(docs)

    def _generate_generic_docs(self, code: str, language: str) -> str:
        """Generate generic documentation"""
        return f"""# {language.title()} Code Documentation

## Overview
This document provides documentation for the {language} code.

## Code Structure
- Lines of code: {len(code.split(chr(10)))}
- Language: {language}

## Usage
[Add usage instructions]

## Notes
[Add additional notes]
"""

class PerformanceMonitor:
    """Real-time performance monitoring"""

    def __init__(self):
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'api_calls': 0,
            'response_times': [],
            'error_count': 0
        }
        self.monitoring = False

    def start_monitoring(self):
        """Start background performance monitoring"""
        if not self.monitoring:
            self.monitoring = True
            threading.Thread(target=self._monitor_loop, daemon=True).start()

    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                # Collect system metrics
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent

                self.metrics['cpu_usage'].append(cpu_percent)
                self.metrics['memory_usage'].append(memory_percent)

                # Keep only last 100 measurements
                if len(self.metrics['cpu_usage']) > 100:
                    self.metrics['cpu_usage'] = self.metrics['cpu_usage'][-100:]
                if len(self.metrics['memory_usage']) > 100:
                    self.metrics['memory_usage'] = self.metrics['memory_usage'][-100:]

                time.sleep(5)  # Update every 5 seconds

            except Exception as e:
                logging.error(f"Performance monitoring error: {e}")

    def get_current_stats(self) -> Dict:
        """Get current performance statistics"""
        return {
            'cpu_avg': sum(self.metrics['cpu_usage'][-10:]) / min(10, len(self.metrics['cpu_usage'])) if self.metrics['cpu_usage'] else 0,
            'memory_avg': sum(self.metrics['memory_usage'][-10:]) / min(10, len(self.metrics['memory_usage'])) if self.metrics['memory_usage'] else 0,
            'api_calls': self.metrics['api_calls'],
            'avg_response_time': sum(self.metrics['response_times']) / len(self.metrics['response_times']) if self.metrics['response_times'] else 0,
            'error_count': self.metrics['error_count']
        }

class ErrorTracker:
    """Advanced error tracking and analysis"""

    def __init__(self):
        self.errors = []
        self.error_patterns = defaultdict(int)

    def track_error(self, error: str, context: str = ""):
        """Track an error occurrence"""
        error_entry = {
            'timestamp': datetime.now(),
            'error': error,
            'context': context,
            'id': str(uuid.uuid4())
        }

        self.errors.append(error_entry)
        self.error_patterns[self._categorize_error(error)] += 1

        # Keep only last 1000 errors
        if len(self.errors) > 1000:
            self.errors = self.errors[-1000:]

    def _categorize_error(self, error: str) -> str:
        """Categorize error type"""
        error_lower = error.lower()

        if 'syntax' in error_lower:
            return 'syntax_error'
        elif 'import' in error_lower or 'module' in error_lower:
            return 'import_error'
        elif 'permission' in error_lower:
            return 'permission_error'
        elif 'network' in error_lower or 'connection' in error_lower:
            return 'network_error'
        else:
            return 'other_error'

    def get_error_summary(self) -> Dict:
        """Get error summary and patterns"""
        return {
            'total_errors': len(self.errors),
            'error_patterns': dict(self.error_patterns),
            'recent_errors': self.errors[-5:] if self.errors else []
        }

class UsageAnalytics:
    """Usage analytics and insights"""

    def __init__(self):
        self.usage_data = {
            'commands_used': defaultdict(int),
            'languages_used': defaultdict(int),
            'session_duration': 0,
            'features_used': defaultdict(int)
        }
        self.session_start = datetime.now()

    def track_command(self, command: str):
        """Track command usage"""
        self.usage_data['commands_used'][command] += 1

    def track_language(self, language: str):
        """Track language usage"""
        self.usage_data['languages_used'][language] += 1

    def track_feature(self, feature: str):
        """Track feature usage"""
        self.usage_data['features_used'][feature] += 1

    def get_analytics_summary(self) -> Dict:
        """Get usage analytics summary"""
        session_duration = (datetime.now() - self.session_start).total_seconds()

        return {
            'session_duration': session_duration,
            'most_used_commands': dict(sorted(self.usage_data['commands_used'].items(), key=lambda x: x[1], reverse=True)[:5]),
            'most_used_languages': dict(sorted(self.usage_data['languages_used'].items(), key=lambda x: x[1], reverse=True)[:5]),
            'most_used_features': dict(sorted(self.usage_data['features_used'].items(), key=lambda x: x[1], reverse=True)[:5])
        }

class GitManager:
    def __init__(self):
        self.git_commands = {
            'status': 'git status --porcelain',
            'add_all': 'git add .',
            'commit': 'git commit -m',
            'push': 'git push',
            'pull': 'git pull',
            'branch': 'git branch',
            'checkout': 'git checkout',
            'merge': 'git merge',
            'log': 'git log --oneline -10'
        }

    def git_operation(self, operation: str, args: str = "") -> str:
        """Perform Git operations"""
        try:
            if operation not in self.git_commands:
                return f"❌ Unknown git operation: {operation}"

            command = self.git_commands[operation]
            if args:
                command += f" {args}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                return f"✅ Git {operation} successful:\n{output}" if output else f"✅ Git {operation} completed"
            else:
                error = result.stderr.strip()
                return f"❌ Git {operation} failed:\n{error}"

        except subprocess.TimeoutExpired:
            return f"⏰ Git {operation} timed out"
        except Exception as e:
            return f"❌ Git operation error: {str(e)}"

    def get_git_status(self) -> Dict:
        """Get comprehensive git status"""
        try:
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'],
                capture_output=True,
                text=True,
                timeout=10
            )

            branch_result = subprocess.run(
                ['git', 'branch', '--show-current'],
                capture_output=True,
                text=True,
                timeout=10
            )

            return {
                'is_git_repo': status_result.returncode == 0,
                'current_branch': branch_result.stdout.strip() if branch_result.returncode == 0 else 'unknown',
                'modified_files': status_result.stdout.strip().split('\n') if status_result.stdout.strip() else [],
                'has_changes': bool(status_result.stdout.strip())
            }

        except Exception as e:
            return {
                'is_git_repo': False,
                'error': str(e)
            }

    def auto_commit_and_push(self, message: str = "Auto-commit by AI Agent") -> str:
        """Automatically commit and push changes"""
        try:
            # Check if there are changes
            status = self.get_git_status()
            if not status.get('has_changes'):
                return "✅ No changes to commit"

            # Add all changes
            add_result = self.git_operation('add_all')
            if '❌' in add_result:
                return add_result

            # Commit changes
            commit_result = self.git_operation('commit', f'"{message}"')
            if '❌' in commit_result:
                return commit_result

            # Push changes
            push_result = self.git_operation('push')
            return push_result

        except Exception as e:
            return f"❌ Auto commit/push error: {str(e)}"

class PackageManager:
    def __init__(self):
        self.managers = {
            'python': {
                'install': 'pip install',
                'uninstall': 'pip uninstall -y',
                'list': 'pip list',
                'update': 'pip install --upgrade',
                'requirements': 'pip freeze > requirements.txt'
            },
            'node': {
                'install': 'npm install',
                'uninstall': 'npm uninstall',
                'list': 'npm list',
                'update': 'npm update',
                'requirements': 'npm init -y'
            },
            'rust': {
                'install': 'cargo add',
                'uninstall': 'cargo remove',
                'list': 'cargo tree',
                'update': 'cargo update',
                'requirements': 'cargo init'
            }
        }

    def detect_project_type(self, directory: str = ".") -> str:
        """Auto-detect project type based on files"""
        files = os.listdir(directory)

        if 'package.json' in files:
            return 'node'
        elif 'requirements.txt' in files or any(f.endswith('.py') for f in files):
            return 'python'
        elif 'Cargo.toml' in files:
            return 'rust'
        elif 'pom.xml' in files:
            return 'java'
        elif 'composer.json' in files:
            return 'php'
        else:
            return 'unknown'

    def install_package(self, package: str, project_type: str = None) -> str:
        """Install a package using appropriate package manager"""
        try:
            if not project_type:
                project_type = self.detect_project_type()

            if project_type not in self.managers:
                return f"❌ Unsupported project type: {project_type}"

            command = f"{self.managers[project_type]['install']} {package}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes for package installation
            )

            if result.returncode == 0:
                return f"✅ Successfully installed {package} using {project_type} package manager"
            else:
                error = result.stderr.strip()
                return f"❌ Failed to install {package}:\n{error}"

        except subprocess.TimeoutExpired:
            return f"⏰ Package installation timed out"
        except Exception as e:
            return f"❌ Package installation error: {str(e)}"

    def manage_dependencies(self, action: str, package: str = "", project_type: str = None) -> str:
        """Manage project dependencies"""
        try:
            if not project_type:
                project_type = self.detect_project_type()

            if project_type not in self.managers:
                return f"❌ Unsupported project type: {project_type}"

            if action not in self.managers[project_type]:
                return f"❌ Unsupported action: {action}"

            command = self.managers[project_type][action]
            if package:
                command += f" {package}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                return f"✅ {action.title()} completed:\n{output}" if output else f"✅ {action.title()} completed"
            else:
                error = result.stderr.strip()
                return f"❌ {action.title()} failed:\n{error}"

        except Exception as e:
            return f"❌ Dependency management error: {str(e)}"

class AdvancedCodeAnalyzer:
    """Advanced code analysis with AST parsing and security checks"""

    def __init__(self):
        self.supported_languages = ['python', 'javascript', 'typescript', 'java', 'cpp', 'rust', 'go']

    def deep_analyze_code(self, code: str, language: str = "python") -> 'CodeAnalysisResult':
        """Perform deep code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = CodeAnalysisResult()

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis.functions.append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis.classes.append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis.imports.append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis.imports.append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis.variables.append(target.id)

                    # Calculate complexity (simplified)
                    analysis.complexity = len(analysis.functions) + len(analysis.classes)

                    # Basic security checks
                    if 'eval' in code or 'exec' in code:
                        analysis.security_issues.append("Use of eval/exec detected")
                    if 'input(' in code and 'int(' not in code:
                        analysis.security_issues.append("Unvalidated user input")

                    return analysis

                except SyntaxError as e:
                    analysis = CodeAnalysisResult()
                    analysis.syntax_errors.append(str(e))
                    return analysis
            else:
                # Basic analysis for other languages
                analysis = CodeAnalysisResult()
                analysis.lines = len(code.split('\n'))
                analysis.characters = len(code)
                return analysis

        except Exception as e:
            analysis = CodeAnalysisResult()
            analysis.syntax_errors.append(f"Analysis error: {str(e)}")
            return analysis

@dataclass
class CodeAnalysisResult:
    """Result of code analysis"""
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    complexity: int = 0
    security_issues: List[str] = field(default_factory=list)
    syntax_errors: List[str] = field(default_factory=list)
    lines: int = 0
    characters: int = 0

class RefactoringEngine:
    """Advanced code refactoring engine"""

    def __init__(self):
        self.refactor_patterns = {
            'python': {
                'extract_function': r'def\s+(\w+)\([^)]*\):',
                'extract_class': r'class\s+(\w+)(?:\([^)]*\))?:',
                'remove_duplicates': r'(\w+)\s*=\s*(\w+)\s*=\s*(.+)',
            }
        }

    def auto_refactor(self, code: str, language: str = "python") -> str:
        """Automatically refactor code for better structure"""
        try:
            if language.lower() == "python":
                # Simple refactoring suggestions
                suggestions = []

                # Check for long functions
                functions = re.findall(r'def\s+(\w+)\([^)]*\):(.*?)(?=def|\Z)', code, re.DOTALL)
                for func_name, func_body in functions:
                    lines = func_body.strip().split('\n')
                    if len(lines) > 20:
                        suggestions.append(f"Function '{func_name}' is too long ({len(lines)} lines). Consider breaking it down.")

                # Check for code duplication
                lines = code.split('\n')
                line_counts = {}
                for line in lines:
                    stripped = line.strip()
                    if stripped and not stripped.startswith('#'):
                        line_counts[stripped] = line_counts.get(stripped, 0) + 1

                duplicates = [line for line, count in line_counts.items() if count > 1]
                if duplicates:
                    suggestions.append(f"Found {len(duplicates)} duplicate lines that could be refactored.")

                if suggestions:
                    return "🔄 Refactoring Suggestions:\n" + "\n".join([f"• {s}" for s in suggestions])
                else:
                    return "✅ Code structure looks good!"
            else:
                return f"🔄 Basic refactoring analysis for {language} - consider modularizing large functions"

        except Exception as e:
            return f"❌ Refactoring error: {str(e)}"

class LanguageConverter:
    """Cross-language code conversion engine"""

    def __init__(self):
        self.conversion_patterns = {
            'python_to_javascript': {
                'print': 'console.log',
                'def ': 'function ',
                'True': 'true',
                'False': 'false',
                'None': 'null',
            },
            'javascript_to_python': {
                'console.log': 'print',
                'function ': 'def ',
                'true': 'True',
                'false': 'False',
                'null': 'None',
            }
        }

    def convert_code(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        try:
            conversion_key = f"{from_lang.lower()}_to_{to_lang.lower()}"

            if conversion_key in self.conversion_patterns:
                converted_code = code
                patterns = self.conversion_patterns[conversion_key]

                for old_pattern, new_pattern in patterns.items():
                    converted_code = converted_code.replace(old_pattern, new_pattern)

                return f"� Converted from {from_lang} to {to_lang}:\n```{to_lang}\n{converted_code}\n```"
            else:
                return f"❌ Conversion from {from_lang} to {to_lang} not yet supported"

        except Exception as e:
            return f"❌ Code conversion error: {str(e)}"

class WebScraper:
    """Enhanced web scraping and information retrieval"""

    def __init__(self):
        self.search_engines = {
            'stackoverflow': 'https://stackoverflow.com/search?q=',
            'github': 'https://github.com/search?q=',
            'docs_python': 'https://docs.python.org/3/search.html?q=',
            'mdn': 'https://developer.mozilla.org/en-US/search?q='
        }

    def enhanced_web_search(self, query: str, context: str = "") -> str:
        """Enhanced web search with multiple sources"""
        try:
            # Simple implementation - in a real scenario, you'd use proper APIs
            results = []

            # Add context to query if available
            if context:
                enhanced_query = f"{query} {context}"
            else:
                enhanced_query = query

            # Simulate search results
            results.append(f"🔍 Search results for: {enhanced_query}")
            results.append("📚 Stack Overflow: Found relevant discussions about error handling")
            results.append("🐙 GitHub: Located example repositories with similar implementations")
            results.append("� Documentation: Official docs with best practices")

            return "\n".join(results)

        except Exception as e:
            return f"❌ Web search error: {str(e)}"

# Main execution
if __name__ == "__main__":
    try:
        print("🚀 Initializing Advanced CLI Coding Agent...")
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully!")
        agent.run_agent()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        print("🔧 Please check your environment and dependencies.")
        """Show comprehensive agent status"""
        try:
            # Get Git status
            git_status = self.git_manager.get_git_status()

            # Get project type
            project_type = self.package_manager.detect_project_type()

            # Get predictive suggestions
            predictions = self.context.predictive_cache.next_actions[:3]

            # Get performance metrics
            import psutil
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent()

            print(f"""
📊 ADVANCED AGENT STATUS DASHBOARD:

🏠 ENVIRONMENT:
• Current Directory: {self.context.current_directory}
• Project Type: {project_type.title()}
• Operating System: {os.name}
• Memory Usage: {memory_percent}%
• CPU Usage: {cpu_percent}%

📁 PROJECT CONTEXT:
• Active Files: {len(self.context.active_files)} files
• Command History: {len(self.context.command_history)} commands
• Working Memory: {len(self.context.working_memory)} items
• Cache Size: {len(self.cache)} cached items
• Conversation Memory: {len(self.memory.buffer)} messages

🔄 GIT STATUS:
• Repository: {'✅ Active' if git_status.get('is_git_repo') else '❌ Not a Git repo'}
• Current Branch: {git_status.get('current_branch', 'N/A')}
• Changes: {'✅ Clean' if not git_status.get('has_changes') else f"⚠️ {len(git_status.get('modified_files', []))} modified files"}

🧠 INTELLIGENCE STATUS:
• Predictive Cache: {'✅ Active' if predictions else '⏸️ Idle'}
• Background Processing: ✅ Running
• Pattern Analysis: ✅ Learning
• Last Error: {self.context.last_error or '✅ None'}

� PREDICTIVE SUGGESTIONS:
{chr(10).join([f"  • {pred}" for pred in predictions]) if predictions else "  • No predictions available"}

📁 RECENT FILES:
{chr(10).join([f"  • {Path(f).name} ({Path(f).suffix})" for f in self.context.active_files[-5:]]) if self.context.active_files else "  • No recent files"}

⚡ RECENT COMMANDS:
{chr(10).join([f"  • {cmd[:50]}{'...' if len(cmd) > 50 else ''}" for cmd in self.context.command_history[-3:]]) if self.context.command_history else "  • No recent commands"}

🎯 CAPABILITIES STATUS:
• Code Analysis: ✅ Ready
• Cross-Language Conversion: ✅ Ready
• Security Auditing: ✅ Ready
• Performance Profiling: ✅ Ready
• Web Research: ✅ Ready
• Package Management: ✅ Ready
• Git Operations: ✅ Ready
• Multi-Step Pipelines: ✅ Ready

💡 QUICK ACTIONS:
• Type 'suggestions' for context-aware recommendations
• Type 'help' for comprehensive capabilities guide
• Type 'pipeline [description]' for automated workflows
""")
        except Exception as e:
            print(f"❌ Error displaying status: {str(e)}")
            print("📊 Basic Status: Agent is running but status details unavailable")

if __name__ == "__main__":
    try:
        print("🚀 Initializing Advanced CLI Coding Agent...")
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully!")
        agent.run_agent()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        print("🔧 Please check your environment and dependencies.")