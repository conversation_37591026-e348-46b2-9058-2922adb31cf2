# Core AI and Language Processing
langchain>=0.1.0
langchain-google-genai>=1.0.0
google-generativeai>=0.3.0

# Modern Terminal UI
rich>=13.0.0
textual>=0.50.0

# Async Operations
aiohttp>=3.9.0
aiofiles>=23.0.0
asyncio-throttle>=1.0.0

# System Monitoring and Performance
psutil>=5.9.0
memory-profiler>=0.61.0

# Web Scraping and Requests
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0

# Code Analysis and Parsing
tree-sitter>=0.20.0
ast-decompiler>=0.7.0
pylint>=3.0.0
flake8>=6.0.0
black>=23.0.0

# File Operations and Utilities
watchdog>=3.0.0
pathlib2>=2.3.0
python-magic>=0.4.0

# Data Processing
pandas>=2.1.0
numpy>=1.24.0
pyyaml>=6.0.0

# Environment and Configuration
python-dotenv>=1.0.0
configparser>=6.0.0

# Security and Encryption
cryptography>=41.0.0
hashlib-compat>=1.0.0

# Testing and Quality
pytest>=7.4.0
pytest-asyncio>=0.21.0
coverage>=7.3.0

# Development Tools
ipython>=8.15.0
jupyter>=1.0.0

# Optional: Advanced Features
# Uncomment if you want additional capabilities
# tensorflow>=2.13.0  # For ML-based code analysis
# torch>=2.0.0        # For advanced AI features
# transformers>=4.30.0 # For local language models
# opencv-python>=4.8.0 # For image processing
# pillow>=10.0.0      # For image handling
