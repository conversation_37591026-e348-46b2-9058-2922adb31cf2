# Advanced AI Coding Agent Configuration
# Copy this file to .env and fill in your API keys

# Primary Gemini API Key (Required)
GEMINI_API_KEY=your_primary_gemini_api_key_here

# Additional API Keys for Load Balancing (Optional)
# Add more keys to enable load balancing and higher rate limits
GEMINI_API_KEY_2=your_secondary_gemini_api_key_here
GEMINI_API_KEY_3=your_tertiary_gemini_api_key_here
# GEMINI_API_KEY_4=your_fourth_gemini_api_key_here
# GEMINI_API_KEY_5=your_fifth_gemini_api_key_here

# OpenAI API Key (Optional - for future OpenAI integration)
# OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (Optional - for future Claude integration)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Agent Configuration
AGENT_NAME=Advanced_AI_Coding_Agent
AGENT_VERSION=3.0
MAX_WORKERS=16
MEMORY_LIMIT=20

# UI Configuration
UI_THEME=dark
ENABLE_STREAMING=true
SHOW_PREDICTIONS=true
AUTO_SAVE=true

# Performance Settings
TOKEN_OPTIMIZATION=true
RESPONSE_CACHING=true
PREDICTIVE_PREFETCH=true
SMART_COMPRESSION=true

# Rate Limiting (requests per minute)
RATE_LIMIT_RPM=60
RATE_LIMIT_TPM=100000

# Security Settings
ENABLE_SECURITY_SCAN=true
ENABLE_VULNERABILITY_CHECK=true
SANDBOX_MODE=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=agent.log
ENABLE_PERFORMANCE_LOGGING=true

# Development Settings
DEBUG_MODE=false
VERBOSE_OUTPUT=false
ENABLE_PROFILING=false

# File Management
MAX_FILE_SIZE_MB=100
BACKUP_FILES=true
AUTO_CLEANUP=true

# Web Scraping Settings
USER_AGENT=Advanced_AI_Coding_Agent/3.0
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# Database Settings (Optional - for future database integration)
# DATABASE_URL=sqlite:///agent.db
# ENABLE_DATABASE=false

# Cloud Integration (Optional)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AZURE_SUBSCRIPTION_ID=your_azure_subscription_id
# GCP_PROJECT_ID=your_gcp_project_id
