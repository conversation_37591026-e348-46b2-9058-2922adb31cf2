#!/usr/bin/env python3
"""
Advanced AI Coding Agent Setup Script
Automated installation and configuration
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print setup banner"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              🤖 Advanced AI Coding Agent v3.0                ║
║                    Setup & Installation                      ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        print("✅ Dependencies installed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("💡 Try running manually: pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Setup environment configuration"""
    print("\n🔧 Setting up environment...")
    
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if not env_file.exists() and env_template.exists():
        shutil.copy(env_template, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env with your API keys!")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ No .env template found")
        return False
    
    return True

def check_optional_tools():
    """Check for optional development tools"""
    print("\n🛠 Checking optional tools...")
    
    tools = {
        "git": "Git version control",
        "node": "Node.js for JavaScript projects",
        "cargo": "Rust toolchain",
        "java": "Java development kit"
    }
    
    for tool, description in tools.items():
        try:
            subprocess.run([tool, "--version"], 
                          capture_output=True, check=True)
            print(f"✅ {description} available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"⚠️  {description} not found (optional)")

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "logs",
        "cache",
        "backups",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created {directory}/ directory")

def run_initial_test():
    """Run initial test to verify installation"""
    print("\n🧪 Running initial test...")
    
    try:
        # Import main modules to check if everything works
        import rich
        from langchain_google_genai import ChatGoogleGenerativeAI
        import aiohttp
        import psutil
        
        print("✅ All core modules imported successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Try reinstalling dependencies")
        return False

def display_next_steps():
    """Display next steps for user"""
    next_steps = """
🎉 Setup completed successfully!

📋 Next Steps:
1. Edit .env file with your Gemini API keys:
   - Get API keys from: https://makersuite.google.com/app/apikey
   - Add GEMINI_API_KEY=your_key_here

2. Run the agent:
   python main.py

3. Try these commands:
   🤖 agent> help                    # Show all capabilities
   🤖 agent> status                  # Real-time dashboard
   🤖 agent> Create a Python script  # Generate code

💡 Pro Tips:
- Add multiple API keys for load balancing
- Use 'suggestions' command for smart recommendations
- Check README.md for detailed usage examples

🚀 Happy coding with your AI assistant!
    """
    print(next_steps)

def main():
    """Main setup function"""
    print_banner()
    
    # Check system requirements
    check_python_version()
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check optional tools
    check_optional_tools()
    
    # Run initial test
    if not run_initial_test():
        print("\n⚠️  Setup completed with warnings. Check error messages above.")
    else:
        print("\n✅ Setup completed successfully!")
    
    # Display next steps
    display_next_steps()

if __name__ == "__main__":
    main()
